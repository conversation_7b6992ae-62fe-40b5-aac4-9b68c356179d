# Development Environment - Updated with Spring Boot backend
VITE_API_BASE_URL=http://localhost:8081/api
VITE_JSON_SERVER_URL=http://localhost:8080
VITE_PDF_SERVICE_URL=https://sparkmate-image-service-158250499449.asia-south1.run.app
VITE_APP_URL=http://localhost:5173

# App Configuration
VITE_APP_NAME=Brainstorm

# Payment Gateway Keys (Test Keys - Safe to expose in frontend)
VITE_RAZORPAY_KEY_ID=rzp_test_USZOM02NmjnObZ
VITE_CASHFREE_APP_ID=your_cashfree_test_app_id

# UPI Configuration
VITE_UPI_ID=merchant@paytm

# Firebase Config - Development
VITE_FIREBASE_API_KEY=AIzaSyDgHZQm2OoU8BpD1r9Zj8Nijz3my3dRv-E
VITE_FIREBASE_AUTH_DOMAIN=brainstorm-upsc.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=brainstorm-upsc
VITE_FIREBASE_STORAGE_BUCKET=brainstorm-upsc.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=677811681568
VITE_FIREBASE_APP_ID=1:677811681568:web:363994c7a855f88636ff45
VITE_FIREBASE_MEASUREMENT_ID=G-MFC0XV9JF0


