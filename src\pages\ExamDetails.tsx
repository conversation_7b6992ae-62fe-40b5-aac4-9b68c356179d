import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Clock, Users, Calendar, FileText, Award, ArrowLeft, Play, ShieldCheck, HelpCircle, Trophy } from 'lucide-react';
import { API_ENDPOINTS, apiUtils } from '../config/api';
import moment from 'moment-timezone';

interface Exam {
  id: string;
  title: string;
  description: string;
  duration: number;
  totalQuestions: number;
  totalMarks: number;
  passingMarks: number;
  difficulty: string;
  status: 'active' | 'inactive' | 'completed' | 'draft';
  startTime: string;
  instructions: string;
  attempts: number;
}

const getDerivedExamStatus = (exam: Exam | null): 'upcoming' | 'live' | 'completed' | 'loading' => {
  if (!exam) return 'loading';

  if (exam.status !== 'active') {
    return 'completed';
  }

  const today = moment.utc().startOf('day');
  const examDate = moment.utc(exam.startTime).startOf('day');

  if (examDate.isAfter(today)) {
    return 'upcoming';
  } else if (examDate.isSame(today)) {
    return 'live';
  } else {
    return 'completed';
  }
};

function ExamDetails() {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const [exam, setExam] = useState<Exam | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (examId) {
      fetchExamDetails(examId);
    }
  }, [examId]);

  const fetchExamDetails = async (id: string) => {
    try {
      setLoading(true);
      const response = await apiUtils.get(`${API_ENDPOINTS.EXAMS}/${id}`);
      if (response.ok) {
        setExam(await response.json());
      } else {
        setError('Failed to load exam details.');
      }
    } catch (err) {
      setError('A network error occurred.');
    } finally {
      setLoading(false);
    }
  };

  const handleStartExam = () => {
    navigate(`/app/exams-start/${examId}`);
  };

  if (loading) {
    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
    );
  }

  if (error || !exam) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center text-center px-4">
        <div>
           <HelpCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-xl font-semibold text-gray-800">{error || 'Exam not found'}</p>
          <Link to="/app/exams" className="text-blue-600 font-medium mt-4 inline-block">← Back to Exams</Link>
        </div>
      </div>
    );
  }

  const derivedStatus = getDerivedExamStatus(exam);

  const getActionButton = () => {
    // ########## START OF MINIMAL CHANGE ##########
    switch (derivedStatus) {
      case 'live':
        return (
          <button onClick={handleStartExam} className="w-full sm:w-auto bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 mx-auto animate-pulse">
            <Play className="h-5 w-5" />
            <span>Start Live Exam</span>
          </button>
        );
      case 'completed':
        return (
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <button onClick={handleStartExam} className="w-full sm:w-auto bg-blue-600 text-white px-8 py-3 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2">
                    <Play className="h-5 w-5" />
                    <span>Practice Now</span>
                </button>
                <Link to={`/app/ranking/${exam.id}`} className="w-full sm:w-auto bg-yellow-100 text-yellow-800 px-8 py-3 rounded-xl font-semibold text-lg hover:bg-yellow-200 transition-colors flex items-center justify-center space-x-2">
                    <Trophy className="h-5 w-5" />
                    <span>View Ranking</span>
                </Link>
            </div>
        );
      case 'upcoming':
        return (
          <button disabled className="w-full sm:w-auto bg-gray-400 text-white px-8 py-4 rounded-xl font-semibold text-lg flex items-center justify-center space-x-2 cursor-not-allowed">
            <Clock className="h-5 w-5" />
            <span>Upcoming on {moment(exam.startTime).format('ll')}</span>
          </button>
        );
      default:
        return null;
    }
    // ########## END OF MINIMAL CHANGE ##########
  };


  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <Link to="/app/exams" className="flex items-center text-gray-600 hover:text-gray-900 mb-4 font-medium">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to All Exams
        </Link>
        <div className="bg-white rounded-xl shadow-sm border p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">{exam.title}</h1>
            <p className="text-gray-600 text-lg mt-2">{exam.description}</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8 text-center">
            <div className="p-4 bg-blue-50 rounded-lg"><Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" /><div className="text-2xl font-bold">{exam.duration}</div><div className="text-sm text-gray-600">Minutes</div></div>
            <div className="p-4 bg-green-50 rounded-lg"><FileText className="h-8 w-8 text-green-600 mx-auto mb-2" /><div className="text-2xl font-bold">{exam.totalQuestions}</div><div className="text-sm text-gray-600">Questions</div></div>
            <div className="p-4 bg-yellow-50 rounded-lg"><Award className="h-8 w-8 text-yellow-600 mx-auto mb-2" /><div className="text-2xl font-bold">{exam.totalMarks}</div><div className="text-sm text-gray-600">Max Marks</div></div>
            <div className="p-4 bg-purple-50 rounded-lg"><ShieldCheck className="h-8 w-8 text-purple-600 mx-auto mb-2" /><div className="text-2xl font-bold">{exam.passingMarks}</div><div className="text-sm text-gray-600">To Pass</div></div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-yellow-800">Instructions</h3>
            <div className="text-yellow-700 whitespace-pre-wrap mt-2">{exam.instructions || 'No instructions provided.'}</div>
          </div>
          <div className="text-center mt-4">
            {getActionButton()}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ExamDetails;