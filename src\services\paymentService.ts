// Payment service for direct integration
export interface PaymentConfig {
  razorpay: {
    keyId: string;
  };
  cashfree: {
    appId: string;
    mode: 'sandbox' | 'production';
  };
}

export interface PaymentOrder {
  orderId: string;
  amount: number;
  currency: string;
  materialId: string;
  userId: string;
}

export class PaymentService {
  private config: PaymentConfig;

  constructor(config: PaymentConfig) {
    this.config = config;
  }

  // Generate unique order ID
  generateOrderId(materialId: string, userId: string): string {
    return `order_${materialId}_${userId}_${Date.now()}`;
  }

  // Razorpay integration - simplified for frontend only
  async initiateRazorpayPayment(
    material: any,
    user: any,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      if (typeof (window as any).Razorpay === 'undefined') {
        throw new Error('Razorpay script not loaded');
      }

      // Check if we're using live keys in production
      const isLiveKey = this.config.razorpay.keyId.startsWith('rzp_live_');
      const isProduction = import.meta.env.PROD;

      if (isLiveKey && isProduction) {
        console.warn('Using live Razorpay key in production');
      }

      const orderId = this.generateOrderId(material.id, user.id);

      const options = {
        key: this.config.razorpay.keyId,
        amount: material.price * 100,
        currency: 'INR',
        name: 'Brainstorm',
        description: `Purchase: ${material.title}`,
        handler: (response: any) => {
          onSuccess({
            ...response,
            materialId: material.id,
            userId: user.id,
            amount: material.price,
            orderId: orderId
          });
        },
        prefill: {
          name: user.name,
          email: user.email,
          contact: user.phoneNumber || '9999999999',
        },
        theme: {
          color: '#2563eb',
        },
        modal: {
          ondismiss: () => {
            onError(new Error('Payment cancelled by user'));
          },
        },
        retry: {
          enabled: true,
          max_count: 1
        },
        timeout: 300,
        remember_customer: false
      };

      const razorpay = new (window as any).Razorpay(options);
      razorpay.on('payment.failed', function (response: any) {
        console.error('Payment failed:', response.error);
        onError(new Error(`Payment failed: ${response.error.description}`));
      });

      razorpay.open();
    } catch (error) {
      console.error('Razorpay initialization error:', error);
      onError(error);
    }
  }

  // Alternative approach using Razorpay Payment Links
  async initiateRazorpayPaymentLink(
    material: any,
    user: any,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      // Create payment link URL (this would typically be generated by your backend)
      const paymentLinkData = {
        amount: material.price * 100,
        currency: 'INR',
        description: `Purchase: ${material.title}`,
        customer: {
          name: user.name,
          email: user.email,
          contact: user.phoneNumber || '9999999999'
        },
        callback_url: `${window.location.origin}/payment-success?materialId=${material.id}&userId=${user.id}`,
        callback_method: 'get'
      };

      // For demo: redirect to a mock payment success page
      const successUrl = `${window.location.origin}/payment-success?status=success&materialId=${material.id}&userId=${user.id}&amount=${material.price}`;

      // Open in new tab
      window.open(successUrl, '_blank');

      // Simulate success
      setTimeout(() => {
        onSuccess({
          materialId: material.id,
          userId: user.id,
          amount: material.price,
          paymentMethod: 'payment_link'
        });
      }, 2000);

    } catch (error) {
      onError(error);
    }
  }

  // Cashfree integration - using Cashfree Drop-in checkout
  async initiateCashfreePayment(
    material: any,
    user: any,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      if (typeof (window as any).Cashfree === 'undefined') {
        throw new Error('Cashfree script not loaded');
      }

      const orderId = this.generateOrderId(material.id, user.id);
      const appUrl = import.meta.env.VITE_APP_URL || window.location.origin;

      // Initialize Cashfree
      const cashfree = (window as any).Cashfree({
        mode: this.config.cashfree.mode
      });

      const checkoutOptions = {
        paymentSessionId: `session_${orderId}`,
        returnUrl: `${appUrl}/payment-success?materialId=${material.id}&userId=${user.id}`,
        customerDetails: {
          customerId: user.id.toString(),
          customerName: user.name,
          customerEmail: user.email,
          customerPhone: user.phoneNumber || '9999999999'
        },
        orderDetails: {
          orderId: orderId,
          orderAmount: material.price,
          orderCurrency: 'INR',
          orderNote: `Purchase: ${material.title}`
        },
        theme: {
          color: '#2563eb'
        }
      };

      // For demo purposes - simulate Cashfree checkout
      this.simulateCashfreeCheckout(material, user, orderId, onSuccess, onError);

    } catch (error) {
      onError(error);
    }
  }

  // Simulate Cashfree checkout for demo
  private simulateCashfreeCheckout(
    material: any,
    user: any,
    orderId: string,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): void {
    // Create a modal-like experience
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 10px;
      max-width: 400px;
      width: 90%;
      text-align: center;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    `;

    content.innerHTML = `
      <div style="margin-bottom: 20px;">
        <h3 style="color: #2563eb; margin-bottom: 10px;">Cashfree Payment</h3>
        <p style="color: #666; margin-bottom: 15px;">Complete your payment for:</p>
        <p style="font-weight: bold; color: #333;">${material.title}</p>
        <p style="font-size: 24px; color: #2563eb; font-weight: bold;">₹${material.price}</p>
      </div>
      
      <div style="margin-bottom: 20px;">
        <button id="upi-pay" style="
          background: #00D4AA;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          margin: 5px;
          cursor: pointer;
          font-weight: bold;
        ">Pay with UPI</button>
        
        <button id="card-pay" style="
          background: #2563eb;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          margin: 5px;
          cursor: pointer;
          font-weight: bold;
        ">Pay with Card</button>
      </div>
      
      <div style="margin-top: 20px;">
        <button id="cancel-payment" style="
          background: #dc2626;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
        ">Cancel</button>
      </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // Handle UPI payment
    content.querySelector('#upi-pay')?.addEventListener('click', () => {
      const merchantUPI = import.meta.env.VITE_UPI_ID || 'merchant@paytm';
      const upiUrl = `upi://pay?pa=${merchantUPI}&pn=Brainstorm&am=${material.price}&cu=INR&tn=Purchase ${material.title}&tr=${orderId}`;

      if (navigator.userAgent.match(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i)) {
        window.location.href = upiUrl;

        // Simulate success after delay
        setTimeout(() => {
          document.body.removeChild(modal);
          onSuccess({
            orderId: orderId,
            cf_payment_id: `cf_${Date.now()}`,
            materialId: material.id,
            userId: user.id,
            amount: material.price,
            paymentMethod: 'UPI',
            gateway: 'cashfree'
          });
        }, 3000);
      } else {
        alert('UPI payments are supported on mobile devices only. Please try card payment.');
      }
    });

    // Handle card payment
    content.querySelector('#card-pay')?.addEventListener('click', () => {
      // Simulate card payment flow
      const cardModal = this.createCardPaymentModal(material, user, orderId, onSuccess, onError);
      document.body.removeChild(modal);
      document.body.appendChild(cardModal);
    });

    // Handle cancel
    content.querySelector('#cancel-payment')?.addEventListener('click', () => {
      document.body.removeChild(modal);
      onError(new Error('Payment cancelled by user'));
    });
  }

  // Create card payment modal
  private createCardPaymentModal(
    material: any,
    user: any,
    orderId: string,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): HTMLElement {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 10px;
      max-width: 400px;
      width: 90%;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    `;

    content.innerHTML = `
      <h3 style="color: #2563eb; margin-bottom: 20px; text-align: center;">Card Payment</h3>
      <form id="card-form">
        <div style="margin-bottom: 15px;">
          <label style="display: block; margin-bottom: 5px; font-weight: bold;">Card Number</label>
          <input type="text" placeholder="1234 5678 9012 3456" maxlength="19" style="
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
          " required>
        </div>
        
        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
          <div style="flex: 1;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Expiry</label>
            <input type="text" placeholder="MM/YY" maxlength="5" style="
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 16px;
            " required>
          </div>
          <div style="flex: 1;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">CVV</label>
            <input type="text" placeholder="123" maxlength="3" style="
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 16px;
            " required>
          </div>
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 5px; font-weight: bold;">Cardholder Name</label>
          <input type="text" value="${user.name}" style="
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
          " required>
        </div>
        
        <div style="text-align: center; margin-bottom: 15px;">
          <p style="font-weight: bold; color: #333;">Amount: ₹${material.price}</p>
        </div>
        
        <div style="display: flex; gap: 10px;">
          <button type="submit" style="
            flex: 1;
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
          ">Pay Now</button>
          
          <button type="button" id="cancel-card" style="
            flex: 1;
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
          ">Cancel</button>
        </div>
      </form>
    `;

    modal.appendChild(content);

    // Handle form submission
    content.querySelector('#card-form')?.addEventListener('submit', (e) => {
      e.preventDefault();

      // Show processing
      const submitBtn = content.querySelector('button[type="submit"]') as HTMLButtonElement;
      submitBtn.textContent = 'Processing...';
      submitBtn.disabled = true;

      // Simulate payment processing
      setTimeout(() => {
        document.body.removeChild(modal);
        onSuccess({
          orderId: orderId,
          cf_payment_id: `cf_card_${Date.now()}`,
          materialId: material.id,
          userId: user.id,
          amount: material.price,
          paymentMethod: 'Card',
          gateway: 'cashfree'
        });
      }, 2000);
    });

    // Handle cancel
    content.querySelector('#cancel-card')?.addEventListener('click', () => {
      document.body.removeChild(modal);
      onError(new Error('Payment cancelled by user'));
    });

    return modal;
  }
}






