"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Trophy, Clock, ArrowLeft, Download, CheckCircle, XCircle, MinusCircle, Loader2 } from "lucide-react";
import { API_ENDPOINTS, apiUtils } from "../config/api";

interface ExamResult {
    id: string;
    examId: string;
    userId: string;
    examTitle: string;
    score: number;
    totalQuestions: number;
    totalMarks: number;
    correctAnswers: number;
    incorrectAnswers: number;
    unanswered: number;
    timeTakenMinutes: string;
    rank: number;
    percentile: number;
    completedAt: string;
}

function Results() {
  const { resultId } = useParams<{ resultId: string }>();
  const [result, setResult] = useState<ExamResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!resultId) {
      setError("No result ID found in URL.");
      setLoading(false);
      return;
    }

    const fetchResultData = async () => {
        try {
            setLoading(true);
            // ########## START OF MINIMAL CHANGE ##########
            // Simplified to one API call as the backend now calculates everything on submit.
            const resultResponse = await apiUtils.get(`${API_ENDPOINTS.EXAM_RESULTS}/${resultId}`);
            if (!resultResponse.ok) {
              const errorData = await resultResponse.json().catch(() => ({ message: "Could not load your exam result." }));
              throw new Error(errorData.message);
            }
            const resultData: ExamResult = await resultResponse.json();
            setResult(resultData);
            // The redundant ranking call is now removed.
            // ########## END OF MINIMAL CHANGE ##########
        } catch (err: any) {
            setError(err.message || "An unexpected error occurred.");
        } finally {
            setLoading(false);
        }
    };

    fetchResultData();
  }, [resultId]);


  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Generating your report...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center text-center p-4">
        <div>
            <p className="text-red-600 mb-4 text-xl font-semibold">{error || "Results not found."}</p>
            <Link to="/app/exams" className="text-blue-600 hover:text-blue-700 font-medium"> ← Back to Exams </Link>
        </div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    const percentage = result.totalMarks > 0 ? (score / result.totalMarks) * 100 : 0;
    if (percentage >= 75) return "text-green-600";
    if (percentage >= 40) return "text-yellow-600";
    return "text-red-600";
  };
  
  const getGrade = (score: number) => {
    const percentage = result.totalMarks > 0 ? (score / result.totalMarks) * 100 : 0;
    if (percentage >= 90) return "A+";
    if (percentage >= 80) return "A";
    if (percentage >= 70) return "B+";
    if (percentage >= 60) return "B";
    if (percentage >= 50) return "C";
    return "F";
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link to="/app/exams" className="flex items-center text-gray-600 hover:text-gray-900 font-medium">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Exams
          </Link>
        </div>
        <div className="bg-white rounded-xl shadow-sm border overflow-hidden p-8">
            <div className="text-center mb-8">
              <Trophy className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-gray-900">Exam Report</h1>
              <p className="text-gray-600">{result.examTitle}</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8 text-center">
              <div className="p-6 bg-blue-50 rounded-lg">
                <div className={`text-3xl font-bold ${getScoreColor(result.score)}`}>{result.score}</div>
                <div className="text-sm text-gray-600">Your Score</div>
              </div>
               <div className="p-6 bg-green-50 rounded-lg">
                <div className={`text-3xl font-bold ${getScoreColor(result.score)}`}>{getGrade(result.score)}</div>
                <div className="text-sm text-gray-600">Grade</div>
              </div>
              <div className="p-6 bg-purple-50 rounded-lg">
                <div className="text-3xl font-bold text-gray-900">{result.rank ? `#${result.rank}` : 'N/A'}</div>
                <div className="text-sm text-gray-600">Your Rank</div>
              </div>
              <div className="p-6 bg-yellow-50 rounded-lg">
                <div className="text-3xl font-bold text-gray-900">{Math.round(result.percentile)}%</div>
                <div className="text-sm text-gray-600">Percentile</div>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg border mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                    <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2"/>
                    <p className="text-2xl font-bold">{result.correctAnswers}</p>
                    <p className="text-sm text-gray-600">Correct</p>
                </div>
                <div>
                    <XCircle className="h-8 w-8 text-red-500 mx-auto mb-2"/>
                    <p className="text-2xl font-bold">{result.incorrectAnswers}</p>
                    <p className="text-sm text-gray-600">Incorrect</p>
                </div>
                <div>
                    <MinusCircle className="h-8 w-8 text-gray-500 mx-auto mb-2"/>
                    <p className="text-2xl font-bold">{result.unanswered}</p>
                    <p className="text-sm text-gray-600">Unanswered</p>
                </div>
              </div>
            </div>

             {/* The ranking snapshot is no longer needed as rank/percentile is in the main result */}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2">
                    <Download className="h-5 w-5" />
                    <span>Download Report</span>
                </button>
                <Link to={`/app/ranking/${result.examId}`} className="bg-yellow-100 text-yellow-800 px-6 py-3 rounded-lg hover:bg-yellow-200 flex items-center justify-center space-x-2">
                    <Trophy className="h-5 w-5" />
                    <span>View Full Leaderboard</span>
                </Link>
                <Link to="/app/exams" className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 text-center">
                    Take Another Exam
                </Link>
            </div>
        </div>
      </div>
    </div>
  );
}

export default Results;