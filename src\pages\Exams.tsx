import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Clock, Users, Calendar, Filter, Search, FileText, Award, Eye } from 'lucide-react';
import { API_ENDPOINTS, apiUtils } from '../config/api';
import moment from 'moment-timezone';

interface Exam {
  id: string;
  title: string;
  description: string;
  duration: number;
  totalQuestions: number;
  attempts: number;
  difficulty: string;
  status: string;
  startTime: string;
  endTime: string;
  category: string;
  score?: number;
}

function Exams() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchExams();
  }, []);

  const fetchExams = async () => {
    try {
      setLoading(true);
      const response = await apiUtils.get(API_ENDPOINTS.EXAMS);
      if (response.ok) {
        setExams(await response.json());
      } else {
        setError('Failed to fetch exams');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // ########## START OF MINIMAL CHANGE ##########
  const getDerivedExamStatus = (exam: Exam): 'upcoming' | 'live' | 'completed' => {
    if (exam.status !== 'active') {
        return 'completed';
    }

    const today = moment.utc().startOf('day');
    const examDate = moment.utc(exam.startTime).startOf('day');

    if (examDate.isAfter(today)) {
      return 'upcoming';
    } else if (examDate.isSame(today)) {
      return 'live';
    } else {
      return 'completed';
    }
  };
  // ########## END OF MINIMAL CHANGE ##########

  const filteredExams = exams.filter(exam => {
    if (exam.status === 'draft' || exam.status === 'inactive') return false;

    const derivedStatus = getDerivedExamStatus(exam);
    const matchesFilter = filter === 'all' || derivedStatus === filter;
    const matchesSearch = exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center text-center">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  const getStatusBadge = (status: 'upcoming' | 'live' | 'completed') => {
    switch (status) {
      case 'upcoming':
        return <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">Upcoming</span>;
      case 'live':
        return <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium animate-pulse">Live Today</span>;
      case 'completed':
        return <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">Completed</span>;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    return difficulty === 'Easy' ? 'text-green-600 bg-green-100' :
           difficulty === 'Medium' ? 'text-yellow-600 bg-yellow-100' :
           'text-red-600 bg-red-100';
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">Exams</h1>

      <div className="bg-white p-6 rounded-xl shadow-sm border">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-gray-600" />
            {['all', 'live', 'upcoming', 'completed'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status)}
                className={`px-4 py-2 rounded-lg text-sm font-medium capitalize transition-colors ${filter === status ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
              >
                {status}
              </button>
            ))}
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input type="text" placeholder="Search exams..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10 pr-4 py-2 border rounded-lg w-64"/>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredExams.map((exam) => (
          <div key={exam.id} className="relative group bg-white rounded-xl shadow-sm border overflow-hidden">
            <div className="p-6 transition-all duration-300 group-hover:blur-sm">
              <div className="flex justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{exam.title}</h3>
                  <p className="text-sm text-gray-600 mt-1 h-10">{exam.description}</p>
                </div>
                <span className={`px-2 py-1 h-fit rounded-full text-xs font-medium ${getDifficultyColor(exam.difficulty)}`}>
                  {exam.difficulty}
                </span>
              </div>
              <div className="flex space-x-4 text-sm text-gray-500 mb-4">
                <span className="flex items-center"><Clock className="h-4 w-4 mr-1" />{exam.duration} min</span>
                <span className="flex items-center"><FileText className="h-4 w-4 mr-1" />{exam.totalQuestions} Qs</span>
                <span className="flex items-center"><Users className="h-4 w-4 mr-1" />{exam.attempts} attempts</span>
              </div>
              <div className="border-t pt-4 flex justify-between items-center">
                {getStatusBadge(getDerivedExamStatus(exam))}
                <span className="flex items-center text-sm text-gray-500"><Calendar className="h-4 w-4 mr-1" />{moment(exam.startTime).format("ll")}</span>
              </div>
            </div>
            <Link to={`/app/exams/${exam.id}`} className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-25 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
              <span className="bg-blue-200 text-black font-semibold py-2 px-5 rounded-lg shadow-md flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>View Details</span>
              </span>
            </Link>
          </div>
        ))}
      </div>
      {filteredExams.length === 0 && (
        <div className="text-center py-12"><p>No exams found.</p></div>
      )}
    </div>
  );
}

export default Exams;