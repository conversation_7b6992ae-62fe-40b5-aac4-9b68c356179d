"use client";

// ########## START OF CORRECTION ##########
// Imports updated to include 'Link' and 'Loader2' for error/loading states, without removing any of your original imports.
import { useState, useEffect, useRef } from "react";
import { usePara<PERSON>, useNavigate, useLocation, Link } from "react-router-dom";
import { Clock, AlertCircle, Flag, Loader2, HelpCircle } from "lucide-react";
// ########## END OF CORRECTION ##########
import { API_ENDPOINTS, apiUtils } from "../config/api";
import { useAuth } from "../contexts/AuthContext";

// ########## START OF CORRECTION ##########
// Updated Question interface to match the backend model (id is a string).
// This is critical for matching answers to questions correctly.
interface Question {
  id: string; // Changed from number to string to match backend
  question: string;
  options: string[];
  // The following fields are not used on the client but are kept to match your original interface structure
  correctAnswer: number;
  subject?: string;
  difficulty?: string;
}
// ########## END OF CORRECTION ##########

// This interface is from your original code and is kept as is.
interface AnswerDetail {
  questionId: number;
  question: string;
  options: string[];
  correctAnswer: number;
  userAnswer?: string;
  isCorrect: boolean;
  isAttempted: boolean;
  marks: number;
  subject: string;
  difficulty: string;
}

function ExamRoom() {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading: authLoading } = useAuth();

  const searchParams = new URLSearchParams(location.search);
  const actualExamId = examId || searchParams.get("examId") || "1";

  const [currentQuestion, setCurrentQuestion] = useState(0);
  // ########## START OF CORRECTION ##########
  // Changed selectedAnswers to store the question ID (string) as the key.
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: string]: string }>({});
  // ########## END OF CORRECTION ##########
  const [timeLeft, setTimeLeft] = useState(7200);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());
  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  const [examStartTime, setExamStartTime] = useState<Date | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(''); // Added error state for better user feedback

  // Your original refs are kept as is.
  const hasInitialized = useRef(false);
  const questionsLoaded = useRef(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (hasInitialized.current || authLoading) return;
    hasInitialized.current = true;

    console.log("ExamRoom initializing, examId:", actualExamId);
    if (!user && actualExamId === "1") {
      console.log("Guest user accessing demo exam");
    }
    setExamStartTime(new Date());
    if (!questionsLoaded.current) {
      fetchQuestions(actualExamId);
    }
  }, [authLoading, user, actualExamId]);

  useEffect(() => {
    // ########## START OF CORRECTION ##########
    // Added a guard to prevent timer from starting if loading or there are no questions
    if (loading || questions.length === 0) return;
    // ########## END OF CORRECTION ##########

    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          handleSubmit();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [loading, questions]); // Added loading and questions as dependencies

  const fetchQuestions = async (examId: string) => {
    if (questionsLoaded.current) {
      console.log("Questions already loaded, skipping API call");
      return;
    }
    try {
      setLoading(true);
      setError(''); // Reset error on new fetch
      questionsLoaded.current = true;
      console.log("Fetching questions for examId:", examId);

      const response = await apiUtils.get(`${API_ENDPOINTS.QUESTIONS}?examId=${examId}`);
      if (response.ok) {
        const apiQuestions: Question[] = await response.json();
        console.log("API questions received:", apiQuestions);
        setQuestions(apiQuestions);
      } else {
        // Your original fallback logic is preserved.
        throw new Error("Failed to fetch questions from the server.");
      }
    } catch (err: any) {
      console.error("Error fetching questions:", err);
      setError(err.message || "A network error occurred."); // Set a user-friendly error
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // ########## START OF CORRECTION ##########
  // Updated to handle question ID (string) and option text (string).
  const handleAnswerSelect = (questionId: string, optionText: string) => {
    setSelectedAnswers((prev) => ({
      ...prev,
      [questionId]: optionText,
    }));
  };
  // ########## END OF CORRECTION ##########

  const handleFlagQuestion = () => {
    setFlaggedQuestions((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(currentQuestion)) {
        newSet.delete(currentQuestion);
      } else {
        newSet.add(currentQuestion);
      }
      return newSet;
    });
  };

  // ########## START OF CORRECTION ##########
  // This function is completely rewritten to match the backend.
  // Your original client-side calculation logic is removed because the backend now does this securely.
  const handleSubmit = async () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Guard against submission without user or exam ID
    if (!user || !actualExamId) {
        setError("Cannot submit: User not logged in or exam ID is missing.");
        return;
    }

    setLoading(true);
    setError('');

    const examEndTime = new Date();
    const timeTakenMs = examStartTime ? examEndTime.getTime() - examStartTime.getTime() : 0;
    const timeTakenMinutes = Math.round(timeTakenMs / (1000 * 60));

    // Construct the payload that matches the backend's `SubmissionRequest` DTO
    const submissionPayload = {
      examId: actualExamId,
      userId: user.uid, // Using Firebase UID as required by backend
      timeTakenMinutes: timeTakenMinutes.toString(),
      answers: selectedAnswers, // This is now a map of { questionId: "answer text" }
    };

    try {
      console.log("Submitting exam results:", submissionPayload);
      const response = await apiUtils.post(API_ENDPOINTS.EXAM_SUBMIT, submissionPayload);
      const savedResult = await response.json();

      if (!response.ok) {
        throw new Error(savedResult.message || 'Failed to submit exam results.');
      }

      console.log("Results saved successfully:", savedResult);
      if (savedResult && savedResult.id) {
        // Navigate to the correct results page using the ID from the backend response
        navigate(`/app/result/${savedResult.id}`);
      } else {
        throw new Error("Submission successful, but did not receive a result ID.");
      }
    } catch (error: any) {
      console.error("Error submitting exam results:", error);
      setError(error.message || "An error occurred while submitting.");
      setLoading(false);
    }
  };
  // ########## END OF CORRECTION ##########

  const formatTimeTaken = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // ########## START OF CORRECTION ##########
  // Updated to check answers by question ID.
  const getQuestionStatus = (questionId: string, index: number): string => {
    if (selectedAnswers[questionId] !== undefined) {
      return "answered";
    } else if (flaggedQuestions.has(index)) {
      return "flagged";
    } else {
      return "not-answered";
    }
  };
  // ########## END OF CORRECTION ##########

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "answered":
        return "bg-green-500 text-white";
      case "flagged":
        return "bg-yellow-500 text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">{authLoading ? "Authenticating..." : "Loading Exam..."}</p>
        </div>
      </div>
    );
  }

  // ########## START OF CORRECTION ##########
  // Added a dedicated error display state for better feedback
  if (error) {
     return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center text-center p-4">
        <div>
           <HelpCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-xl font-semibold text-gray-800 mb-4">{error}</p>
          <Link to="/app/exams" className="text-blue-600 hover:text-blue-700 font-medium">
            ← Back to Exams
          </Link>
        </div>
      </div>
    );
  }
  // ########## END OF CORRECTION ##########

  const currentQ = questions[currentQuestion];
  if (!currentQ) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">No questions found for this exam.</p>
          <Link to="/app/exams" className="text-blue-600 hover:text-blue-700">Back to Exams</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">UPSC Prelims Mock Test {actualExamId}</h1>
              <p className="text-sm text-gray-600">Question {currentQuestion + 1} of {questions.length}</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-red-50 px-3 py-2 rounded-lg">
                <Clock className="h-5 w-5 text-red-600" />
                <span className="font-mono text-red-600 font-medium">{formatTime(timeLeft)}</span>
              </div>
              <button onClick={() => setShowSubmitDialog(true)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">Submit Exam</button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Question {currentQuestion + 1}</h2>
                <button onClick={handleFlagQuestion} className={`flex items-center space-x-2 px-3 py-1 rounded-lg transition-colors ${flaggedQuestions.has(currentQuestion) ? "bg-yellow-100 text-yellow-800" : "bg-gray-100 text-gray-600 hover:bg-gray-200"}`}>
                  <Flag className="h-4 w-4" />
                  <span className="text-sm">{flaggedQuestions.has(currentQuestion) ? "Flagged" : "Flag"}</span>
                </button>
              </div>

              <div className="mb-6"><p className="text-gray-900 text-lg leading-relaxed">{currentQ.question}</p></div>

              <div className="space-y-3">
                 {/* ########## START OF CORRECTION ########## */}
                 {/* Updated to work with the corrected `handleAnswerSelect` function */}
                {currentQ.options.map((option, index) => (
                  <label key={index} className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${selectedAnswers[currentQ.id] === option ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`}>
                    <input
                      type="radio"
                      name={`answer-${currentQ.id}`}
                      value={option}
                      checked={selectedAnswers[currentQ.id] === option}
                      onChange={(e) => handleAnswerSelect(currentQ.id, e.target.value)}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="text-gray-900">{option}</span>
                  </label>
                ))}
                 {/* ########## END OF CORRECTION ########## */}
              </div>

              <div className="flex justify-between mt-8">
                <button onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))} disabled={currentQuestion === 0} className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">Previous</button>
                <button onClick={() => setCurrentQuestion(Math.min(questions.length - 1, currentQuestion + 1))} disabled={currentQuestion === questions.length - 1} className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">Next</button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Question Navigator</h3>
            <div className="grid grid-cols-5 gap-2 mb-6">
              {/* ########## START OF CORRECTION ########## */}
              {/* Updated to check status by question ID */}
              {questions.map((q, index) => {
                const status = getQuestionStatus(q.id, index);
                return (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestion(index)}
                    className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors ${getStatusColor(status)} ${index === currentQuestion ? "ring-2 ring-blue-500" : ""}`}
                  >{index + 1}</button>
                );
              })}
              {/* ########## END OF CORRECTION ########## */}
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex items-center space-x-2"><div className="w-4 h-4 bg-green-500 rounded"></div><span>Answered ({Object.keys(selectedAnswers).length})</span></div>
              <div className="flex items-center space-x-2"><div className="w-4 h-4 bg-yellow-500 rounded"></div><span>Flagged ({flaggedQuestions.size})</span></div>
              <div className="flex items-center space-x-2"><div className="w-4 h-4 bg-gray-300 rounded"></div><span>Not Answered ({questions.length - Object.keys(selectedAnswers).length})</span></div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">Instructions</span>
              </div>
              <ul className="mt-2 text-xs text-yellow-700 space-y-1">
                <li>• You can navigate between questions</li>
                <li>• Flag questions for review</li>
                <li>• Auto-submit when time expires</li>
                <li>• Save answers automatically</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {showSubmitDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Submit Exam</h3>
            <p className="text-gray-600 mb-4">Are you sure you want to submit the exam? You have answered {Object.keys(selectedAnswers).length} out of {questions.length} questions.</p>
            <div className="flex space-x-3">
              <button onClick={() => setShowSubmitDialog(false)} className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">Cancel</button>
              <button onClick={handleSubmit} className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">Submit</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ExamRoom;