// Environment detection
const isDevelopment = import.meta.env.DEV || import.meta.env.NODE_ENV === "development"
const isProduction = import.meta.env.PROD || import.meta.env.NODE_ENV === "production"

// API Configuration
export const API_CONFIG = {
  // Spring Boot Backend API - Environment-based URLs
  API_BASE_URL:
    import.meta.env.VITE_API_BASE_URL ||
    (isProduction ? "https://backend-dot-brainstorm-upsc-466216.el.r.appspot.com/api" : "http://localhost:8081/api"),
  // Spring Boot PDF Service (deployed)
  PDF_SERVICE_URL:
    import.meta.env.VITE_PDF_SERVICE_URL || "https://sparkmate-image-service-158250499449.asia-south1.run.app",
  // Legacy JSON server URL (deprecated - keeping for fallback)
  JSON_SERVER_URL: import.meta.env.VITE_JSON_SERVER_URL || "http://localhost:8080",
  APP_NAME: import.meta.env.VITE_APP_NAME || "Brainstorm",
  APP_URL:
    import.meta.env.VITE_APP_URL ||
    (isProduction
      ? "https://brainstorm-upsc-466216.el.r.appspot.com" // Update with your actual production URL
      : "http://localhost:5173"),
  // Environment info
  ENVIRONMENT: isProduction ? "production" : "development",
  IS_DEVELOPMENT: isDevelopment,
  IS_PRODUCTION: isProduction,
}

// Log environment and API configuration
console.log("🔧 API Configuration:", {
  environment: API_CONFIG.ENVIRONMENT,
  isDevelopment: API_CONFIG.IS_DEVELOPMENT,
  isProduction: API_CONFIG.IS_PRODUCTION,
  apiBaseUrl: API_CONFIG.API_BASE_URL,
  appUrl: API_CONFIG.APP_URL,
  nodeEnv: import.meta.env.NODE_ENV,
  mode: import.meta.env.MODE,
  dev: import.meta.env.DEV,
  prod: import.meta.env.PROD,
})

export const API_ENDPOINTS = {
  // Authentication (Note: Spring Boot doesn't have auth endpoints yet)
  LOGIN: `${API_CONFIG.API_BASE_URL}/auth/login`,
  REGISTER: `${API_CONFIG.API_BASE_URL}/auth/register`,
  // Users
  USERS: `${API_CONFIG.API_BASE_URL}/users`,
  USER_PROFILES: `${API_CONFIG.API_BASE_URL}/userProfiles`,
  CURRENT_USER: `${API_CONFIG.API_BASE_URL}/users/me`,

  CURRENT_USER_PROFILE: `${API_CONFIG.API_BASE_URL}/userProfiles/me`,

    GET_USER_PROFILE_BY_ID: (userId: string) =>
    `${API_CONFIG.API_BASE_URL}/userProfiles/me?userId=${userId}`,

  // Content endpoints
  TESTIMONIALS: `${API_CONFIG.API_BASE_URL}/testimonials`,
  PRICING_PLANS: `${API_CONFIG.API_BASE_URL}/pricingPlans`,
  LANDING_STATS: `${API_CONFIG.API_BASE_URL}/landingStats`,
  LANDING_FEATURES: `${API_CONFIG.API_BASE_URL}/landingFeatures`,
  // News items
  NEWS_ITEMS: `${API_CONFIG.API_BASE_URL}/newsItems`,
  // Exam endpoints
  EXAMS: `${API_CONFIG.API_BASE_URL}/exams`,
  
  QUESTIONS: `${API_CONFIG.API_BASE_URL}/questions`,
  EXAM_RESULTS: `${API_CONFIG.API_BASE_URL}/exam-results`,
  EXAM_SUBMIT: `${API_CONFIG.API_BASE_URL}/exam-results/submit`,
  // Study materials
  STUDY_MATERIALS: `${API_CONFIG.API_BASE_URL}/studyMaterials`,
  // Dashboard data
  PROGRESS_DATA: `${API_CONFIG.API_BASE_URL}/progressData`,
  UPCOMING_EXAMS: `${API_CONFIG.API_BASE_URL}/exams/upcoming`,
  RECENT_MATERIALS: `${API_CONFIG.API_BASE_URL}/recentMaterials`,
  // Admin endpoints
  ADMIN_STATS: `${API_CONFIG.API_BASE_URL}/adminStats`,
  USER_GROWTH_DATA: `${API_CONFIG.API_BASE_URL}/userGrowthData`,
  RECENT_ACTIVITIES: `${API_CONFIG.API_BASE_URL}/recentActivities`,
  NOTIFICATIONS: `${API_CONFIG.API_BASE_URL}/notifications`,

  // NEW: Daily Quiz Endpoints - Correctly configured as requested
  GET_DAILY_QUIZ_QUESTIONS_BY_DATE: (date: string) =>
    `${API_CONFIG.API_BASE_URL}/daily-quizzes/questions/by-date?date=${date}`,
  SUBMIT_DAILY_QUIZ: `${API_CONFIG.API_BASE_URL}/daily-quiz-submissions/submit`,
  GET_USER_DAILY_QUIZ_PROGRESS: 
    `${API_CONFIG.API_BASE_URL}/userDailyQuizProgress/by-user`,
  GET_USER_DAILY_QUIZ_SUBMISSIONS: (userId: string) =>
    `${API_CONFIG.API_BASE_URL}/daily-quiz-submissions/by-user?userId=${userId}`,

  DEMO_QUESTIONS: `${API_CONFIG.API_BASE_URL}/demoQuestions`,
  // Download endpoints
  SECURE_DOWNLOAD: `${API_CONFIG.API_BASE_URL}/studyMaterials/secure-download`,
  DOWNLOAD_LIMITS: `${API_CONFIG.API_BASE_URL}/studyMaterials/download-limits`,
  DOWNLOAD_ATTEMPTS: `${API_CONFIG.API_BASE_URL}/downloadAttempts`,
  DOWNLOAD_PROGRESS: `${API_CONFIG.API_BASE_URL}/studyMaterials/download-progress`,
  DOWNLOAD_COMPLETE: `${API_CONFIG.API_BASE_URL}/studyMaterials/download-complete`,
  // Legacy JSON server URL for backward compatibility
  JSON_SERVER_URL: API_CONFIG.JSON_SERVER_URL,
}

// Utility function to build URLs
export const buildUrl = (baseUrl: string, path: string, params?: Record<string, string>) => {
  let url = `${baseUrl}${path}`
  if (params) {
    const searchParams = new URLSearchParams(params)
    url += `?${searchParams.toString()}`
  }
  return url
}

// Public endpoints that don't require authentication
export const PUBLIC_ENDPOINTS = [
  API_ENDPOINTS.TESTIMONIALS,
  API_ENDPOINTS.PRICING_PLANS,
  API_ENDPOINTS.LANDING_STATS,
  API_ENDPOINTS.LANDING_FEATURES,
  API_ENDPOINTS.NEWS_ITEMS,
  API_ENDPOINTS.DEMO_QUESTIONS,
  API_ENDPOINTS.LOGIN,
  API_ENDPOINTS.REGISTER,
  `${API_CONFIG.API_BASE_URL}/health`,
  `${API_CONFIG.API_BASE_URL}/swagger-ui.html`,
  `${API_CONFIG.API_BASE_URL}/v3/api-docs`,
]

// Demo exam endpoints that don't require authentication
export const DEMO_EXAM_ENDPOINTS = [
  `${API_ENDPOINTS.EXAMS}/1`, // Demo exam
  `${API_ENDPOINTS.QUESTIONS}?examId=1`, // Demo questions
]

// FIXED: Add request cache to prevent duplicate calls
const requestCache = new Map<string, Promise<Response>>()
const CACHE_DURATION = 5000 // 5 seconds

// FIXED: Add request deduplication
const clearCacheEntry = (key: string) => {
  setTimeout(() => {
    requestCache.delete(key)
  }, CACHE_DURATION)
}

// API utility functions
export const apiUtils = {
  // Check if endpoint is public
  isPublicEndpoint: (url: string): boolean => {
    return (
      PUBLIC_ENDPOINTS.some((endpoint) => url.includes(endpoint)) ||
      DEMO_EXAM_ENDPOINTS.some((endpoint) => url.includes(endpoint)) ||
      (url.includes("/dailyQuizQuestions") && url.includes("demo=true"))
    )
  },

  // Get authentication headers
  getAuthHeaders: async (): Promise<HeadersInit> => {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      Accept: "application/json",
    }
    try {
      // Get Firebase user and token
      const { getAuth } = await import("firebase/auth")
      const auth = getAuth()
      const user = auth.currentUser
      if (user) {
        const token = await user.getIdToken()
        console.log(token)
        headers["Authorization"] = `Bearer ${token}`
        console.log("Added Firebase token to request headers")
      } else {
        console.warn("No authenticated user found for API request")
      }
    } catch (error) {
      console.error("Error getting Firebase token:", error)
    }
    return headers
  },

  // FIXED: Make authenticated API request with deduplication
  authenticatedFetch: async (url: string, options: RequestInit = {}): Promise<Response> => {
    // FIXED: Create cache key for GET requests to prevent duplicates
    const method = options.method || "GET"
    const cacheKey = `${method}:${url}`
    // FIXED: Return cached promise for duplicate GET requests
    if (method === "GET" && requestCache.has(cacheKey)) {
      console.log("Returning cached request for:", url)
      return requestCache.get(cacheKey)!
    }

    const isPublic = apiUtils.isPublicEndpoint(url)
    let headers: HeadersInit = {
      "Content-Type": "application/json",
      Accept: "application/json",
    }

    // Add authentication headers for protected endpoints
    if (!isPublic) {
      const authHeaders = await apiUtils.getAuthHeaders()
      headers = { ...headers, ...authHeaders }
    } else {
      console.log("Public endpoint detected, skipping authentication:", url)
    }

    // Merge with any existing headers
    const finalOptions: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    }

    console.log("Making API request to:", url, "with headers:", headers)

    // FIXED: Create and cache the request promise
    const requestPromise = fetch(url, finalOptions)

    // FIXED: Cache GET requests only
    if (method === "GET") {
      requestCache.set(cacheKey, requestPromise)
      clearCacheEntry(cacheKey)
    }

    return requestPromise
  },

  // GET request with authentication
  get: async (url: string, options: RequestInit = {}): Promise<Response> => {
    return apiUtils.authenticatedFetch(url, { ...options, method: "GET" })
  },

  // POST request with authentication
  post: async (url: string, data?: any, options: RequestInit = {}): Promise<Response> => {
    const body = data ? JSON.stringify(data) : undefined
    return apiUtils.authenticatedFetch(url, { ...options, method: "POST", body })
  },

  // FIXED: Multipart form data upload method
  postMultipart: async (url: string, formData: FormData, options: RequestInit = {}): Promise<Response> => {
    const isPublic = apiUtils.isPublicEndpoint(url)
    const headers: HeadersInit = {}

    // Add authentication headers for protected endpoints (but NOT Content-Type)
    if (!isPublic) {
      try {
        const { getAuth } = await import("firebase/auth")
        const auth = getAuth()
        const user = auth.currentUser
        if (user) {
          const token = await user.getIdToken()
          headers["Authorization"] = `Bearer ${token}`
          console.log("Added Firebase token to multipart request")
        } else {
          console.warn("No authenticated user found for multipart request")
        }
      } catch (error) {
        console.error("Error getting Firebase token for multipart:", error)
      }
    }

    // Merge with any existing headers (but don't override Content-Type)
    const finalOptions: RequestInit = {
      ...options,
      method: "POST",
      headers: {
        ...headers,
        ...options.headers,
      },
      body: formData,
    }

    console.log("Making multipart API request to:", url, "with headers:", headers)
    return fetch(url, finalOptions)
  },

  // PUT request with authentication
  put: async (url: string, data?: any, options: RequestInit = {}): Promise<Response> => {
    const body = data ? JSON.stringify(data) : undefined
    return apiUtils.authenticatedFetch(url, { ...options, method: "PUT", body })
  },

  // DELETE request with authentication
  delete: async (url: string, options: RequestInit = {}): Promise<Response> => {
    return apiUtils.authenticatedFetch(url, { ...options, method: "DELETE" })
  },
}
