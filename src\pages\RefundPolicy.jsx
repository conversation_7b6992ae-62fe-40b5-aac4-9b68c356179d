import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Shield, CreditCard, AlertCircle, Mail, Globe } from 'lucide-react';

function RefundPolicy() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link
              to="/"
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">Refund Policy</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Refund Policy</h1>
            <p className="text-gray-600">Effective Date: 25 July 2025</p>
          </div>

          {/* Introduction */}
          <div className="prose max-w-none mb-8">
            <p className="text-gray-700 leading-relaxed">
              At Brainstorm UPSC, we are committed to delivering high-quality content and mentorship
              to all our aspirants. We understand that circumstances may arise where a refund is
              necessary. This Refund Policy outlines the conditions under which refunds may be
              requested for purchases made on www.brainstormupsc.com.
            </p>
          </div>

          {/* Sections */}
          <div className="space-y-8">
            {/* Digital Products */}
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <CreditCard className="h-5 w-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">Digital Products</h2>
              </div>
              <div className="text-gray-700 space-y-2">
                <p>
                  Once purchased, digital products such as PDF notes, test series, eBooks, and
                  booster cards are non-refundable and non-transferable.
                </p>
                <p>
                  Due to the nature of downloadable content, we do not offer refunds once the
                  material has been accessed or downloaded.
                </p>
              </div>
            </section>

            {/* Technical Issues */}
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <AlertCircle className="h-5 w-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">Technical Issues</h2>
              </div>
              <div className="text-gray-700 space-y-2">
                <p>
                  If you encounter a technical error (e.g., payment deducted but access to content
                  not granted), please contact us within 48 hours with proof of payment.
                </p>
                <p>
                  We will investigate the issue and either resolve it or process a refund if the
                  error is on our side.
                </p>
              </div>
            </section>

            {/* Duplicate Payment */}
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <CreditCard className="h-5 w-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">Duplicate Payment</h2>
              </div>
              <div className="text-gray-700 space-y-2">
                <p>
                  In case of a duplicate payment, the extra amount will be refunded within 5-7
                  business days after verification.
                </p>
              </div>
            </section>

            {/* How to Request a Refund */}
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <Shield className="h-5 w-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">How to Request a Refund</h2>
              </div>
              <div className="text-gray-700 space-y-2">
                <p>To request a refund, please follow these steps:</p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>Email us at: <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700"><EMAIL></a></li>
                  <li>Include the following details:</li>
                  <ul className="list-disc list-inside ml-6 space-y-1">
                    <li>Full Name</li>
                    <li>Registered Mobile Number</li>
                    <li>Order ID</li>
                    <li>Reason for Refund</li>
                  </ul>
                  <li>Refunds, if approved, will be processed back to the original payment method within 7-10 working days.</li>
                </ol>
              </div>
            </section>
          </div>

          {/* Contact */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Contact Us</h3>
              <p className="text-gray-600 mb-4">
                For any questions or concerns about this Refund Policy, you can contact us at:
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span className="text-gray-700">Email: </span>
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <Globe className="h-4 w-4 text-blue-600" />
                  <span className="text-gray-700">Website: </span>
                  <a href="https://www.brainstormupsc.com" className="text-blue-600 hover:text-blue-700">
                    www.brainstormupsc.com
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RefundPolicy;