import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  BookOpen,
  Trophy,
  Clock,
  Target,
  Play,
  Quote,
  Award,
  TrendingUp,
  Shield,
  Zap,
  Globe,
  Phone,
  Mail,
  MapPin,
  ChevronDown,
  Menu,
  X,
  FileText,
  MessageCircle,
  Send
} from 'lucide-react';
import { API_ENDPOINTS, apiUtils } from '../config/api';

function LandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [pricingPlans, setPricingPlans] = useState<any[]>([]);
  const [stats, setStats] = useState<any[]>([]);
  const [features, setFeatures] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLandingPageData();
  }, []);

  const fetchLandingPageData = async () => {
    try {
      setLoading(true);
      const [testimonialsRes, pricingRes, statsRes, featuresRes] = await Promise.all([
        apiUtils.get(API_ENDPOINTS.TESTIMONIALS),
        apiUtils.get(API_ENDPOINTS.PRICING_PLANS),
        apiUtils.get(API_ENDPOINTS.LANDING_STATS),
        apiUtils.get(API_ENDPOINTS.LANDING_FEATURES)
      ]);

      if (testimonialsRes.ok) {
        const testimonialsData = await testimonialsRes.json();
        setTestimonials(testimonialsData.filter((t: any) => t.featured));
      }

      if (pricingRes.ok) {
        const pricingData = await pricingRes.json();
        setPricingPlans(pricingData.filter((p: any) => p.active));
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData);
      } else {
        console.log('Stats API failed, using fallback data');
        setStats([
          { number: "50,000+", label: "Active Students" },
          { number: "2,500+", label: "Success Stories" },
          { number: "95%", label: "Success Rate" },
          { number: "24/7", label: "Support Available" }
        ]);
      }

      if (featuresRes.ok) {
        const featuresData = await featuresRes.json();
        setFeatures(featuresData);
      } else {
        console.log('Features API failed, using fallback data');
        setFeatures([
          {
            icon: "FileText",
            title: "Weekly Mock Tests",
            description: "UPSC-pattern tests with detailed analysis and performance insights",
            color: "bg-blue-100 text-blue-600"
          },
          {
            icon: "BookOpen",
            title: "Premium Study Materials",
            description: "Curated content by top educators and subject matter experts",
            color: "bg-green-100 text-green-600"
          },
          {
            icon: "TrendingUp",
            title: "Progress Tracking",
            description: "AI-powered analytics to track your preparation and improvement",
            color: "bg-purple-100 text-purple-600"
          },
          {
            icon: "Users",
            title: "Expert Mentorship",
            description: "One-on-one guidance from successful IAS officers and educators",
            color: "bg-orange-100 text-orange-600"
          }
        ]);
      }
    } catch (err) {
      console.error('Error fetching landing page data:', err);
      // Set fallback data for all
      setStats([
        { number: "50,000+", label: "Active Students" },
        { number: "2,500+", label: "Success Stories" },
        { number: "95%", label: "Success Rate" },
        { number: "24/7", label: "Support Available" }
      ]);
      setFeatures([
        {
          icon: "FileText",
          title: "Weekly Mock Tests",
          description: "UPSC-pattern tests with detailed analysis and performance insights",
          color: "bg-blue-100 text-blue-600"
        },
        {
          icon: "BookOpen",
          title: "Premium Study Materials",
          description: "Curated content by top educators and subject matter experts",
          color: "bg-green-100 text-green-600"
        },
        {
          icon: "TrendingUp",
          title: "Progress Tracking",
          description: "AI-powered analytics to track your preparation and improvement",
          color: "bg-purple-100 text-purple-600"
        },
        {
          icon: "Users",
          title: "Expert Mentorship",
          description: "One-on-one guidance from successful IAS officers and educators",
          color: "bg-orange-100 text-orange-600"
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const icons: { [key: string]: any } = {
      FileText,
      BookOpen,
      TrendingUp,
      Users
    };
    return icons[iconName] || FileText;
  };

  useEffect(() => {
    if (testimonials.length > 0) {
      const interval = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [testimonials.length]);

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    setIsMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">B</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Brainstorm UPSC</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <button onClick={() => scrollToSection('features')} className="text-gray-600 hover:text-blue-600 transition-colors">Features</button>
              <button onClick={() => scrollToSection('testimonials')} className="text-gray-600 hover:text-blue-600 transition-colors">Success Stories</button>
              <button onClick={() => scrollToSection('pricing')} className="text-gray-600 hover:text-blue-600 transition-colors">Pricing</button>
              <Link to="/latest-happenings" className="text-gray-600 hover:text-blue-600 transition-colors">Latest Happenings</Link>
              <Link to="/daily-quiz" className="text-gray-600 hover:text-blue-600 transition-colors">Daily Quiz</Link>
              <button onClick={() => scrollToSection('contact')} className="text-gray-600 hover:text-blue-600 transition-colors">Contact</button>
              <Link to="/login" className="text-blue-600 hover:text-blue-700 font-medium">Login</Link>
              <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Start Free Trial
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200">
              <div className="flex flex-col space-y-4">
                <button onClick={() => scrollToSection('features')} className="text-left text-gray-600 hover:text-blue-600 transition-colors">Features</button>
                <button onClick={() => scrollToSection('testimonials')} className="text-left text-gray-600 hover:text-blue-600 transition-colors">Success Stories</button>
                <button onClick={() => scrollToSection('pricing')} className="text-left text-gray-600 hover:text-blue-600 transition-colors">Pricing</button>
                <Link to="/latest-happenings" className="text-left text-gray-600 hover:text-blue-600 transition-colors">Latest Happenings</Link>
                <button onClick={() => scrollToSection('contact')} className="text-left text-gray-600 hover:text-blue-600 transition-colors">Contact</button>
                <Link to="/login" className="text-blue-600 hover:text-blue-700 font-medium">Login</Link>
                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center">
                  Start Free Trial
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                  <Trophy className="h-4 w-4" />
                  <span>India's #1 UPSC Platform</span>
                </div>
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Crack UPSC with
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"> Confidence</span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Join 50,000+ UPSC aspirants who trust our proven methodology. Get weekly mock tests, premium study materials, and expert guidance to achieve your IAS dream.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/register"
                  className="bg-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2 shadow-lg"
                >
                  <span>Start Free Trial</span>
                  <ArrowRight className="h-5 w-5" />
                </Link>
                <Link
                  to="/daily-quiz"
                  className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-600 hover:text-white transition-all flex items-center justify-center space-x-2"
                >
                  <Clock className="h-5 w-5" />
                  <span>Daily Quiz</span>
                </Link>
                <Link
                  to="/latest-happenings"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:border-blue-600 hover:text-blue-600 transition-all flex items-center justify-center space-x-2"
                >
                  <Globe className="h-5 w-5" />
                  <span>Latest Happenings</span>
                </Link>
              </div>

              <div className="flex items-center space-x-8 pt-4">
                <div className="flex items-center space-x-2">
                  <div className="flex -space-x-2">
                    <img className="w-8 h-8 rounded-full border-2 border-white" src="https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg" alt="Student" />
                    <img className="w-8 h-8 rounded-full border-2 border-white" src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg" alt="Student" />
                    <img className="w-8 h-8 rounded-full border-2 border-white" src="https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg" alt="Student" />
                  </div>
                  <span className="text-sm text-gray-600">2,500+ success stories</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">4.9/5 rating</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative z-10 bg-white rounded-2xl shadow-2xl p-8">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-semibold text-gray-900">Today's Mock Test</h3>
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Live</span>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">UPSC Prelims Mock Test 16</span>
                      <span className="font-semibold text-gray-900">100 Questions</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Duration</span>
                      <span className="font-semibold text-gray-900">2 Hours</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Participants</span>
                      <span className="font-semibold text-gray-900">1,250+</span>
                    </div>
                  </div>
                  <Link
                    to="/demo-exam"
                    className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors block text-center"
                  >
                    Join Mock Test
                  </Link>

                  {/* Registration Appeal */}
                  <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <p className="text-sm text-amber-800 mb-2">
                      🎯 Want access to <strong>50+ more tests</strong> and detailed analytics?
                    </p>
                    <Link
                      to="/register"
                      className="text-xs bg-amber-600 text-white px-3 py-1 rounded-md hover:bg-amber-700 transition-colors inline-block"
                    >
                      Register Free - Get Full Access
                    </Link>
                  </div>
                </div>
              </div>
              <div className="absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full opacity-20 blur-3xl"></div>
              <div className="absolute -bottom-4 -left-4 w-72 h-72 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-20 blur-3xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Crack UPSC
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive platform provides all the tools and resources you need for successful UPSC preparation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const IconComponent = getIconComponent(feature.icon);
              return (
                <div key={index} className="text-center">
                  <div className={`w-16 h-16 ${feature.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <IconComponent className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      {/* <section id="testimonials" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Success Stories from Our Students
            </h2>
            <p className="text-xl text-gray-600">
              Hear from UPSC toppers who achieved their dreams with Brainstorm
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : testimonials.length > 0 ? (
            <div className="relative max-w-4xl mx-auto">
              <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-12">
                <Quote className="h-12 w-12 text-blue-600 mb-6" />
                <blockquote className="text-xl lg:text-2xl text-gray-900 mb-8 leading-relaxed">
                  "{testimonials[currentTestimonial]?.quote || ''}"
                </blockquote>
                <div className="flex items-center space-x-4">
                  <img
                    src={testimonials[currentTestimonial]?.image || ''}
                    alt={testimonials[currentTestimonial]?.name || ''}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div>
                    <div className="font-semibold text-gray-900 text-lg">
                      {testimonials[currentTestimonial]?.name || ''}
                    </div>
                    <div className="text-blue-600 font-medium">
                      {testimonials[currentTestimonial]?.rank || ''}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center mt-8 space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${index === currentTestimonial ? 'bg-blue-600' : 'bg-gray-300'
                      }`}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-600">
              <p>No testimonials available at the moment.</p>
            </div>
          )}
        </div>
      </section> */}

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Success Plan
            </h2>
            <p className="text-xl text-gray-600">
              Flexible pricing options to suit every aspirant's needs
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : pricingPlans.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {pricingPlans.map((plan, index) => (
                <div
                  key={index}
                  className={`relative rounded-2xl border-2 p-8 ${plan.popular
                    ? 'border-blue-600 bg-blue-50'
                    : 'border-gray-200 bg-white'
                    } hover:shadow-lg transition-all duration-300`}
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                      <span className="text-gray-600">{plan.period}</span>
                    </div>
                    {plan.originalPrice && (
                      <div className="text-gray-500 line-through text-lg mt-1">
                        {plan.originalPrice}
                      </div>
                    )}
                  </div>

                  <ul className="space-y-4 mb-8">
                    {plan.features?.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Link
                    to="/register"
                    className={`block w-full text-center py-3 px-6 rounded-lg font-semibold transition-colors ${plan.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-900 text-white hover:bg-gray-800'
                      }`}
                  >
                    Get Started
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-600">
              <p>No pricing plans available at the moment.</p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Start Your UPSC Journey?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of successful candidates who chose Brainstorm for their UPSC preparation
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <span>Start Free Trial</span>
              <ArrowRight className="h-5 w-5" />
            </Link>
            <Link
              to="/daily-quiz"
              className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Clock className="h-5 w-5" />
              <span>Try Daily Quiz</span>
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>

      {/* Contact with us Section */}
      <section id="contact" className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Contact with us
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Have questions about your UPSC preparation? Need guidance or support?
              We're here to help you succeed. Reach out to us through any of these channels.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div className="space-y-8">
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h3 className="text-2xl font-semibold text-gray-900 mb-6">Get in Touch</h3>

                {/* Contact Methods */}
                <div className="space-y-6">


                  {/* Telegram */}
                  <div className="flex items-start space-x-4">
                    <div className="bg-blue-100 rounded-lg p-3">
                      <Send className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">Telegram Channel</h4>
                      <p className="text-gray-600 mb-3">
                        Connect with fellow aspirants, get doubt clearing sessions, and access exclusive content.
                      </p>
                      <a
                        href="https://t.me/brainstormupsc"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Send className="h-4 w-4" />
                        <span>Join Telegram Channel</span>
                      </a>
                    </div>
                  </div>

                  {/* WhatsApp */}
                  <div className="flex items-start space-x-4">
                    <div className="bg-green-100 rounded-lg p-3">
                      <MessageCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">WhatsApp Channel</h4>
                      <p className="text-gray-600 mb-3">
                        Join our WhatsApp channel for daily current affairs, study materials, and quick updates.
                      </p>
                      <a
                        href="https://whatsapp.com/channel/0029Vb6fu778PgsCE0AbJY1R"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <MessageCircle className="h-4 w-4" />
                        <span>Join WhatsApp Channel</span>
                      </a>
                    </div>
                  </div>

                  {/* Email */}
                  <div className="flex items-start space-x-4">
                    <div className="bg-purple-100 rounded-lg p-3">
                      <Mail className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">Email Support</h4>
                      <p className="text-gray-600 mb-3">
                        Send us your queries, feedback, or suggestions. We'll get back to you within 24 hours.
                      </p>
                      <a
                        href="mailto:<EMAIL>"
                        className="inline-flex items-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                      >
                        <Mail className="h-4 w-4" />
                        <span>Send Email</span>
                      </a>
                    </div>
                  </div>

                  {/* Phone */}
                  <div className="flex items-start space-x-4">
                    <div className="bg-orange-100 rounded-lg p-3">
                      <Phone className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">Phone Support</h4>
                      <p className="text-gray-600 mb-3">
                        Speak directly with our UPSC experts for personalized guidance and counseling.
                      </p>
                      <a
                        href="tel:8855965237"
                        className="inline-flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
                      >
                        <Phone className="h-4 w-4" />
                        <span>Call Now: +91 88559 65237</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Contact Form */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">Quick Message</h3>
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your email address"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a subject</option>
                    <option value="general-inquiry">General Inquiry</option>
                    <option value="course-information">Course Information</option>
                    <option value="technical-support">Technical Support</option>
                    <option value="billing-payment">Billing & Payment</option>
                    <option value="study-guidance">Study Guidance</option>
                    <option value="feedback">Feedback & Suggestions</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tell us how we can help you..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all transform hover:scale-105 shadow-lg"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>

          {/* Social Media Integration */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-semibold text-gray-900 mb-8">Follow Us for Daily Updates</h3>
            <div className="flex justify-center space-x-6">

              <a
                href="https://t.me/brainstormupsc"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all transform hover:scale-110"
              >
                <Send className="h-8 w-8 text-blue-600 group-hover:text-blue-700" />
              </a>
              <a
                href="https://whatsapp.com/channel/0029Vb6fu778PgsCE0AbJY1R"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all transform hover:scale-110"
              >
                <MessageCircle className="h-8 w-8 text-green-600 group-hover:text-green-700" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="group bg-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all transform hover:scale-110"
              >
                <Mail className="h-8 w-8 text-purple-600 group-hover:text-purple-700" />
              </a>
              <a
                href="tel:8855965237"
                className="group bg-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all transform hover:scale-110"
              >
                <Phone className="h-8 w-8 text-orange-600 group-hover:text-orange-700" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <span className="text-xl font-bold">Brainstorm UPSC</span>
              </div>
              <p className="text-gray-400">
                India's leading online UPSC preparation platform helping thousands of aspirants achieve their IAS dreams.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link to="/about" className="text-gray-400 hover:text-white transition-colors">About Us</Link></li>
                <li><Link to="/latest-happenings" className="text-gray-400 hover:text-white transition-colors">Latest Happenings</Link></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Mock Tests</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Study Materials</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2">
                <li><Link to="/help-center" className="text-gray-400 hover:text-white transition-colors">Help Center</Link></li>
                <li><Link to="/contact" className="text-gray-400 hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link to="/privacy-policy" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link to="/terms-of-service" className="text-gray-400 hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link to="/refund-policy" className="text-gray-400 hover:text-white transition-colors">Refund Policy</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Connect With Us</h4>
              <div className="flex space-x-4">

                <a href="https://t.me/brainstormupsc" target="_blank" rel="noopener noreferrer" className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors" title="Telegram Channel">
                  <Send className="h-5 w-5" />
                </a>
                <a href="https://whatsapp.com/channel/0029Vb6fu778PgsCE0AbJY1R" target="_blank" rel="noopener noreferrer" className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors" title="WhatsApp Channel">
                  <MessageCircle className="h-5 w-5" />
                </a>
                <a href="mailto:<EMAIL>" className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors" title="Email Us">
                  <Mail className="h-5 w-5" />
                </a>
                <a href="tel:8855965237" className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors" title="Call Us">
                  <Phone className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Brainstorm UPSC. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default LandingPage;


