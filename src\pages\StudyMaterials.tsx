"use client"

import { useState, useEffect, useRef } from "react"
import {
  BookOpen,
  Download,
  Search,
  Star,
  ShoppingCart,
  Eye,
  FileText,
  AlertCircle,
  CheckCircle,
  Plus,
} from "lucide-react"
import { useAuth } from "../contexts/AuthContext"
import { API_ENDPOINTS, API_CONFIG, apiUtils } from "../config/api"
import { PaymentService } from "../services/paymentService"
import { PAYMENT_CONFIG } from "../config/payment"
import { useCart } from "../contexts/CartContext"
import { DownloadService } from "../services/downloadService"
import { DownloadProgress } from "../components/DownloadProgress"

const { getAuth } = await import("firebase/auth")

// Initialize payment service
const paymentService = new PaymentService(PAYMENT_CONFIG)
const downloadService = new DownloadService()

// Add interfaces
interface Purchase {
  id: string
  userId: string
  materialId: string
  amount: number
  paymentId: string
  orderId: string
  status: string
  purchaseDate: string
  paymentMethod: string
  gateway?: string
  downloadCount?: number
  lastDownloadAt?: string
}

interface DownloadHistory {
  materialId: string
  downloadDate: string
  downloadUrl: string
  fileName: string
}

function StudyMaterials() {
  const { user } = useAuth()
  const { addToCart, isInCart, removeFromCart } = useCart()
  const [subjectFilter, setSubjectFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [priceFilter, setPriceFilter] = useState("all")
  const [paymentLoading, setPaymentLoading] = useState<number | null>(null)
  const [downloadLoading, setDownloadLoading] = useState<number | null>(null)
  const [paymentError, setPaymentError] = useState("")
  const [materials, setMaterials] = useState<any[]>([])
  const [userPurchases, setUserPurchases] = useState<Purchase[]>([])
  const [downloadHistory, setDownloadHistory] = useState<DownloadHistory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [activeDownloads, setActiveDownloads] = useState<Map<string, string>>(new Map())
  const [downloadLimits, setDownloadLimits] = useState<Map<string, { remaining: number; used: number }>>(new Map())
  const [downloadingMaterials, setDownloadingMaterials] = useState<Set<string>>(new Set())

  // Add refs to prevent duplicate operations
  const downloadInProgressRef = useRef<Set<string>>(new Set())
  const purchasesFetchedRef = useRef(false)

  useEffect(() => {
    fetchMaterials()
  }, [])

  useEffect(() => {
    if (user && !purchasesFetchedRef.current) {
      purchasesFetchedRef.current = true
      fetchUserPurchases()
      loadDownloadHistory()
    }
  }, [user])

  useEffect(() => {
    if (user && materials.length > 0) {
      fetchDownloadLimits()
    }
  }, [user, materials])

  const fetchUserPurchases = async () => {
    if (!user) return

    try {
      // Always load from localStorage first
      const localPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]")
      const userSpecificPurchases = localPurchases.filter((p: Purchase) => p.userId.toString() === user.id.toString())
      console.log("Loaded purchases from localStorage:", {
        allPurchases: localPurchases,
        userSpecific: userSpecificPurchases,
        userId: user.id,
      })
      setUserPurchases(userSpecificPurchases)

      // Try to fetch from Spring Boot API as well (optional)
      try {
        // Use the correct flat endpoint with Firebase UID as userId query parameter
        console.log("Fetching purchases for Firebase UID:", user.id)
        const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/purchases?userId=${user.id}`)
        if (response.ok) {
          const contentType = response.headers.get("content-type")
          if (contentType && contentType.includes("application/json")) {
            const text = await response.text()
            if (text.trim()) {
              const apiPurchases = JSON.parse(text)
              console.log("API purchases:", apiPurchases)
              // Handle both array and single object responses
              // Note: user.id is Firebase UID, so we only compare with string values
              const userPurchasesFromAPI = Array.isArray(apiPurchases)
                ? apiPurchases.filter((p) => p.userId === user.id)
                : apiPurchases.userId === user.id
                  ? [apiPurchases]
                  : []

              if (userPurchasesFromAPI.length > 0) {
                // Merge API purchases with localStorage purchases
                const mergedPurchases = [...userSpecificPurchases]
                userPurchasesFromAPI.forEach((apiPurchase) => {
                  // Only add if not already in localStorage
                  if (!mergedPurchases.find((p) => p.id === apiPurchase.id)) {
                    mergedPurchases.push(apiPurchase)
                  }
                })
                setUserPurchases(mergedPurchases)
                console.log("Merged purchases from API and localStorage:", mergedPurchases)
              }
            }
          }
        }
      } catch (apiError) {
        console.log("API fetch failed, using localStorage only:", apiError)
      }
    } catch (error) {
      console.error("Error loading purchases:", error)
    }
  }

  const loadDownloadHistory = () => {
    if (!user) return
    const history = JSON.parse(localStorage.getItem(`downloadHistory_${user.id}`) || "[]")
    setDownloadHistory(history)
  }

  const saveDownloadHistory = (materialId: string, fileName: string, downloadUrl: string) => {
    if (!user) return
    const newDownload: DownloadHistory = {
      materialId,
      downloadDate: new Date().toISOString(),
      downloadUrl,
      fileName,
    }
    const updatedHistory = [newDownload, ...downloadHistory].slice(0, 50) // Keep last 50 downloads
    setDownloadHistory(updatedHistory)
    localStorage.setItem(`downloadHistory_${user.id}`, JSON.stringify(updatedHistory))
  }

  const isPurchased = (materialId: number | string): boolean => {
    const purchased = userPurchases.some((purchase) => {
      const match = purchase.materialId.toString() === materialId.toString() && purchase.status === "completed"
      console.log("Purchase comparison:", {
        purchaseMaterialId: purchase.materialId,
        purchaseMaterialIdType: typeof purchase.materialId,
        materialId: materialId,
        materialIdType: typeof materialId,
        statusMatch: purchase.status === "completed",
        idMatch: purchase.materialId.toString() === materialId.toString(),
        overallMatch: match,
      })
      return match
    })
    console.log("isPurchased final result:", {
      materialId,
      userPurchases: userPurchases.map((p) => ({
        id: p.materialId,
        type: typeof p.materialId,
        status: p.status,
      })),
      purchased,
    })
    return purchased
  }

  const canDownload = (material: any): boolean => {
    const purchased = isPurchased(material.id)
    console.log("canDownload check:", {
      materialId: material.id,
      purchased,
      hasSecureUrl: !!material.secure_url,
      hasFilePath: !!material.filePath,
    })
    if (!purchased) {
      return false
    }
    // For now, allow download if purchased (we'll add secure_url later)
    return true
  }

  const getPurchaseDetails = (materialId: string): Purchase | null => {
    return (
      userPurchases.find(
        (purchase) => purchase.materialId === materialId.toString() && purchase.status === "completed",
      ) || null
    )
  }

  const handleDownload = async (material: any) => {
    if (!user) {
      alert("Please login to download materials")
      return
    }

    const materialId = material.id.toString()

    // Prevent multiple downloads of same material using ref
    if (downloadInProgressRef.current.has(materialId)) {
      console.log("Download already in progress for material:", materialId)
      return
    }

    // Also check state-based tracking
    if (downloadingMaterials.has(materialId)) {
      console.log("Download already in progress (state check) for material:", materialId)
      return
    }

    const purchased = isPurchased(material.id)
    if (!purchased) {
      alert("Please purchase this material first")
      return
    }

    try {
      // Mark as downloading in both ref and state
      downloadInProgressRef.current.add(materialId)
      setDownloadingMaterials((prev) => new Set(prev).add(materialId))

      // Check download limits
      const remaining = await downloadService.getRemainingDownloads(materialId, user.id)
      console.log("Remaining downloads for material", materialId, ":", remaining)

      if (remaining <= 0) {
        alert("Download limit exceeded! You can only download this material 5 times.")
        // Clean up on error
        downloadInProgressRef.current.delete(materialId)
        setDownloadingMaterials((prev) => {
          const newSet = new Set(prev)
          newSet.delete(materialId)
          return newSet
        })
        return
      }

      // Generate secure download link
      const secureLink = await downloadService.generateSecureDownloadLink(materialId, user.id, {
        userAgent: navigator.userAgent,
      })
      console.log("Generated secure link:", secureLink)

      // Store token for download progress component
      localStorage.setItem(`download_token_${secureLink.downloadId}`, secureLink.token)

      // Start download with progress tracking
      setActiveDownloads((prev) => new Map(prev.set(materialId, secureLink.downloadId)))
    } catch (error) {
      console.error("Download error:", error)
      alert(error.message || "Download failed. Please try again.")
      // Remove from both ref and state on error
      downloadInProgressRef.current.delete(materialId)
      setDownloadingMaterials((prev) => {
        const newSet = new Set(prev)
        newSet.delete(materialId)
        return newSet
      })
    }
  }

  const handleDownloadComplete = async (materialId: string) => {
    console.log("Download completed for material:", materialId)

    // Clean up token
    const downloadId = activeDownloads.get(materialId)
    if (downloadId) {
      localStorage.removeItem(`download_token_${downloadId}`)
    }

    // Remove from active downloads
    setActiveDownloads((prev) => {
      const newMap = new Map(prev)
      newMap.delete(materialId)
      return newMap
    })

    // Remove from both ref and state
    downloadInProgressRef.current.delete(materialId)
    setDownloadingMaterials((prev) => {
      const newSet = new Set(prev)
      newSet.delete(materialId)
      return newSet
    })

    // Refresh download limits after completion
    await fetchDownloadLimits()
  }

  const handleDownloadError = (materialId: string, error: string) => {
    console.log("Download error for material:", materialId, error)

    setActiveDownloads((prev) => {
      const newMap = new Map(prev)
      newMap.delete(materialId)
      return newMap
    })

    // Remove from both ref and state
    downloadInProgressRef.current.delete(materialId)
    setDownloadingMaterials((prev) => {
      const newSet = new Set(prev)
      newSet.delete(materialId)
      return newSet
    })

    alert(`Download failed: ${error}`)
  }

  const getDownloadCount = (materialId: string): number => {
    const purchase = getPurchaseDetails(materialId)
    return purchase?.downloadCount || 0
  }

  const getLastDownloadDate = (materialId: string): string | null => {
    const purchase = getPurchaseDetails(materialId)
    return purchase?.lastDownloadAt || null
  }

  const fetchDownloadLimits = async () => {
    if (!user || materials.length === 0) return

    const limits = new Map()
    for (const material of materials) {
      if (isPurchased(material.id)) {
        try {
          const remaining = await downloadService.getRemainingDownloads(material.id.toString(), user.id)
          const used = Math.max(0, 5 - remaining)
          limits.set(material.id.toString(), {
            remaining,
            used,
          })
        } catch (error) {
          limits.set(material.id.toString(), {
            remaining: 5,
            used: 0,
          })
        }
      }
    }
    setDownloadLimits(limits)
  }

  const renderActionButton = (material: any) => {
    const purchased = isPurchased(material.id)
    const inCart = isInCart(material.id)
    const downloadCount = getDownloadCount(material.id)
    const lastDownload = getLastDownloadDate(material.id)

    console.log("renderActionButton:", {
      materialId: material.id,
      materialTitle: material.title,
      purchased,
      inCart,
      userPurchasesCount: userPurchases.length,
    })

    if (purchased) {
      return (
        <div className="space-y-2">
          {renderDownloadSection(material)}
          <div className="text-xs text-gray-500 text-center">
            <div className="flex items-center justify-center space-x-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Purchased</span>
            </div>
            {downloadCount > 0 && (
              <div className="mt-1">
                Downloaded {downloadCount} time{downloadCount !== 1 ? "s" : ""}
                {lastDownload && <div className="text-xs">Last: {new Date(lastDownload).toLocaleDateString()}</div>}
              </div>
            )}
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-2">
        <button
          onClick={() => (inCart ? removeFromCart(material.id) : addToCart(material))}
          className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
            inCart
              ? "bg-orange-100 text-orange-700 border border-orange-300 hover:bg-orange-200"
              : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"
          }`}
        >
          {inCart ? (
            <>
              <CheckCircle className="h-4 w-4" />
              <span>In Cart</span>
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              <span>Add to Cart</span>
            </>
          )}
        </button>
        <button
          onClick={() => handlePurchase(material)}
          disabled={paymentLoading === material.id}
          className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {paymentLoading === material.id ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Processing...</span>
            </>
          ) : (
            <>
              <ShoppingCart className="h-4 w-4" />
              <span>Buy Now - ₹{material.price}</span>
            </>
          )}
        </button>
      </div>
    )
  }

  const renderDownloadSection = (material: any) => {
    const purchased = isPurchased(material.id)
    const materialId = material.id.toString()
    const activeDownloadId = activeDownloads.get(materialId)
    const limits = downloadLimits.get(materialId)
    const isDownloading = downloadingMaterials.has(materialId) || downloadInProgressRef.current.has(materialId)

    console.log(`Rendering download section for material ${materialId}:`, {
      purchased,
      limits,
      activeDownloadId,
      isDownloading,
    })

    if (!purchased) {
      return null
    }

    // Show download progress if active
    if (activeDownloadId) {
      return (
        <DownloadProgress
          downloadId={activeDownloadId}
          fileName={`${material.title}.pdf`}
          onComplete={() => handleDownloadComplete(materialId)}
          onError={(error) => handleDownloadError(materialId, error)}
        />
      )
    }

    // Show download limits info
    const remainingDownloads = limits?.remaining ?? 5
    const usedDownloads = limits?.used ?? 0

    return (
      <div className="space-y-3">
        <button
          onClick={() => handleDownload(material)}
          disabled={remainingDownloads <= 0 || isDownloading}
          className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
            remainingDownloads <= 0 || isDownloading
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : "bg-green-600 text-white hover:bg-green-700"
          }`}
        >
          {isDownloading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Preparing...</span>
            </>
          ) : (
            <>
              <Download className="h-4 w-4" />
              <span>{remainingDownloads <= 0 ? "Download Limit Reached" : "Download PDF"}</span>
            </>
          )}
        </button>
        {/* Download limits info */}
        <div className="text-xs text-center">
          <div
            className={`px-2 py-1 rounded ${
              remainingDownloads <= 1
                ? "bg-red-50 text-red-600"
                : remainingDownloads <= 2
                  ? "bg-yellow-50 text-yellow-600"
                  : "bg-green-50 text-green-600"
            }`}
          >
            {remainingDownloads} of 5 downloads remaining
          </div>
          {usedDownloads > 0 && (
            <div className="text-gray-500 mt-1">
              Downloaded {usedDownloads} time{usedDownloads !== 1 ? "s" : ""}
            </div>
          )}
        </div>
      </div>
    )
  }

  const fetchMaterials = async () => {
    try {
      setLoading(true)
      const response = await apiUtils.get(API_ENDPOINTS.STUDY_MATERIALS)
      if (response.ok) {
        const data = await response.json()
        setMaterials(data)
      } else {
        setError("Failed to fetch study materials")
      }
    } catch (err) {
      console.error("Error fetching materials:", err)
      setError("Network error. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async (material: any) => {
    if (!user) {
      alert("Please login to purchase materials")
      return
    }

    setPaymentLoading(material.id)
    setPaymentError("")

    try {
      if (typeof (window as any).Razorpay !== "undefined") {
        console.log("Using Razorpay payment gateway")
        await paymentService.initiateRazorpayPayment(material, user, handlePaymentSuccess, handlePaymentError)
      } else {
        console.log("Razorpay not available, using Cashfree as fallback")
        await paymentService.initiateCashfreePayment(material, user, handlePaymentSuccess, handlePaymentError)
      }
    } catch (error) {
      handlePaymentError(error)
    }
  }

  const handlePaymentSuccess = async (response: any) => {
    console.log("Payment success response:", response)
    const purchase: Purchase = {
      id: `purchase_${Date.now()}`,
      userId: response.userId.toString(),
      materialId: response.materialId.toString(),
      amount: response.amount,
      paymentId: response.razorpay_payment_id || response.cf_payment_id,
      orderId: response.orderId,
      status: "completed",
      purchaseDate: new Date().toISOString(),
      paymentMethod: response.paymentMethod || "razorpay",
      gateway: response.gateway || "razorpay",
      downloadCount: 0,
      accessGranted: true, // Add accessGranted field
    }

    console.log("Creating purchase record:", purchase)

    // Save to localStorage
    const existingPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]")
    existingPurchases.push(purchase)
    localStorage.setItem("userPurchases", JSON.stringify(existingPurchases))
    console.log("Updated localStorage purchases:", existingPurchases)

    // Save to backend
    try {
      const auth = getAuth()
      const user = auth.currentUser
      const token = (await user?.getIdToken()) || ""
      const purchaseResponse = await fetch(`${API_CONFIG.API_BASE_URL}/purchases`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`, // Ensure token is included
        },
        body: JSON.stringify(purchase),
      })

      if (!purchaseResponse.ok) {
        console.error("Failed to save purchase to backend:", await purchaseResponse.text())
      } else {
        console.log("Purchase saved to backend successfully")
      }
    } catch (error) {
      console.error("Error saving purchase to backend:", error)
    }

    // Update local state immediately
    setUserPurchases((prev) => {
      const updated = [...prev, purchase]
      console.log("Updated userPurchases state:", updated)
      return updated
    })

    // Find the material to get its title
    const purchasedMaterial = materials.find((m) => m.id.toString() === response.materialId.toString())
    const materialTitle = purchasedMaterial?.title || "Study Material"
    const gatewayName = response.gateway === "cashfree" ? "Cashfree" : "Razorpay"

    alert(`Payment successful via ${gatewayName}! You can now download "${materialTitle}"`)
    setPaymentLoading(null)

    // Force re-render by updating materials state
    setMaterials((prev) => [...prev])
  }

  const handlePaymentError = (error: any) => {
    console.error("Payment failed:", error)
    setPaymentError("Payment failed. Please try again.")
    setPaymentLoading(null)
  }

  const subjects = ["all", "Current Affairs", "Polity", "Economics", "Geography", "History", "Science & Technology"]
  const types = ["all", "PDF", "Video", "Audio"]
  const priceRanges = ["all", "free", "under-200", "200-500", "above-500"]

  const filteredMaterials = materials.filter((material) => {
    const matchesSearch =
      material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSubject = subjectFilter === "all" || material.subject === subjectFilter
    const matchesType = typeFilter === "all" || material.type === typeFilter

    let matchesPrice = true
    if (priceFilter === "free") matchesPrice = material.price === 0
    else if (priceFilter === "under-200") matchesPrice = material.price < 200
    else if (priceFilter === "200-500") matchesPrice = material.price >= 200 && material.price <= 500
    else if (priceFilter === "above-500") matchesPrice = material.price > 500

    return matchesSearch && matchesSubject && matchesType && matchesPrice
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading study materials...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button onClick={fetchMaterials} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Study Materials</h1>
          <p className="text-gray-600 mt-1">Premium PDF documents for UPSC preparation</p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <ShoppingCart className="h-5 w-5 text-gray-600" />
          <span className="text-sm text-gray-600">{userPurchases.length} purchased materials</span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={subjectFilter}
            onChange={(e) => setSubjectFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {subjects.map((subject) => (
              <option key={subject} value={subject}>
                {subject === "all" ? "All Subjects" : subject}
              </option>
            ))}
          </select>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {types.map((type) => (
              <option key={type} value={type}>
                {type === "all" ? "All Types" : type}
              </option>
            ))}
          </select>
          <select
            value={priceFilter}
            onChange={(e) => setPriceFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {priceRanges.map((range) => (
              <option key={range} value={range}>
                {range === "all"
                  ? "All Prices"
                  : range === "free"
                    ? "Free"
                    : range === "under-200"
                      ? "Under ₹200"
                      : range === "200-500"
                        ? "₹200 - ₹500"
                        : "Above ₹500"}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Materials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMaterials.map((material) => (
          <div
            key={material.id}
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">{material.type}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{material.rating}</span>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{material.title}</h3>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{material.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>By {material.author}</span>
                <span className="bg-gray-100 px-2 py-1 rounded text-xs">{material.subject}</span>
              </div>
              <div className="flex items-center text-sm text-gray-500 mb-4">
                <span className="flex items-center">
                  <FileText className="h-4 w-4 mr-1" />
                  {material.pages} pages
                </span>
              </div>
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold text-gray-900">₹{material.price}</span>
                <div className="flex items-center space-x-1">
                  <Download className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-500">{material.downloads}</span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                  <Eye className="h-4 w-4" />
                </button>
                <div className="flex-1">{renderActionButton(material)}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredMaterials.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No materials found</h3>
          <p className="text-gray-600">Try adjusting your filters or search terms.</p>
        </div>
      )}

      {paymentError && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4" />
            <span>{paymentError}</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default StudyMaterials
