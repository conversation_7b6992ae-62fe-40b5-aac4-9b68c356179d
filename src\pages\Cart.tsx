import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON>eft, FileText, Star, Plus, Minus } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { PaymentService } from '../services/paymentService';
import { PAYMENT_CONFIG } from '../config/payment';

const paymentService = new PaymentService(PAYMENT_CONFIG);

function Cart() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { cartItems, cartCount, cartTotal, removeFromCart, clearCart } = useCart();
  const [bulkPaymentLoading, setBulkPaymentLoading] = useState(false);

  const handleBulkPurchase = async () => {
    if (!user) {
      alert('Please login to make purchases');
      return;
    }

    if (cartItems.length === 0) {
      alert('Your cart is empty');
      return;
    }

    setBulkPaymentLoading(true);

    try {
      const bulkOrder = {
        id: `bulk_${Date.now()}`,
        title: `Bulk Purchase (${cartItems.length} items)`,
        price: cartTotal,
        items: cartItems,
        type: 'bulk',
        description: `Bulk purchase of ${cartItems.length} study materials`
      };

      const handleBulkSuccess = (response: any) => {
        const purchases = cartItems.map(item => ({
          id: `purchase_${Date.now()}_${item.materialId}`,
          userId: user.id,
          materialId: item.materialId,
          amount: item.price,
          paymentId: response.razorpay_payment_id || response.cf_payment_id,
          orderId: response.orderId,
          status: 'completed',
          purchaseDate: new Date().toISOString(),
          paymentMethod: response.paymentMethod || 'razorpay',
          gateway: response.gateway || 'razorpay',
          downloadCount: 0,
          bulkOrderId: bulkOrder.id
        }));

        const existingPurchases = JSON.parse(localStorage.getItem('userPurchases') || '[]');
        existingPurchases.push(...purchases);
        localStorage.setItem('userPurchases', JSON.stringify(existingPurchases));

        clearCart();
        navigate('/app/materials');
        
        const gatewayName = response.gateway === 'cashfree' ? 'Cashfree' : 'Razorpay';
        alert(`Bulk payment successful via ${gatewayName}! You can now download all materials.`);
        setBulkPaymentLoading(false);
      };

      const handleBulkError = (error: any) => {
        console.error('Bulk payment failed:', error);
        alert('Bulk payment failed. Please try again.');
        setBulkPaymentLoading(false);
      };

      if (typeof (window as any).Razorpay !== 'undefined') {
        await paymentService.initiateRazorpayPayment(bulkOrder, user, handleBulkSuccess, handleBulkError);
      } else {
        await paymentService.initiateCashfreePayment(bulkOrder, user, handleBulkSuccess, handleBulkError);
      }
    } catch (error) {
      console.error('Error initiating bulk payment:', error);
      alert('Failed to initiate payment. Please try again.');
      setBulkPaymentLoading(false);
    }
  };

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">Add some study materials to get started</p>
          <button
            onClick={() => navigate('/app/materials')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse Materials
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/app/materials')}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
          <p className="text-gray-600 mt-1">{cartCount} items in your cart</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {cartItems.map((item) => (
            <div key={item.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{item.title}</h3>
                  <p className="text-gray-600 text-sm mb-2">By {item.author}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs">{item.subject}</span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 text-yellow-400 fill-current mr-1" />
                      {item.rating}
                    </span>
                    <span>{item.pages} pages</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xl font-bold text-gray-900">₹{item.price}</span>
                    <button
                      onClick={() => removeFromCart(item.materialId)}
                      className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                      title="Remove from cart"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
            
            <div className="space-y-3 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Items ({cartCount})</span>
                <span className="text-gray-900">₹{cartTotal}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Processing Fee</span>
                <span className="text-gray-900">₹0</span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total</span>
                  <span className="text-lg font-bold text-gray-900">₹{cartTotal}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleBulkPurchase}
                disabled={bulkPaymentLoading}
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {bulkPaymentLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <span>Proceed to Payment</span>
                )}
              </button>
              
              <button
                onClick={clearCart}
                className="w-full text-gray-600 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Clear Cart
              </button>
            </div>

            <div className="mt-4 text-xs text-gray-500">
              <p>• Instant download after payment</p>
              <p>• Unlimited re-downloads</p>
              <p>• 24/7 customer support</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Cart;