import{r as t,c as e,g as r,a as n}from"./vendor-CAdiN7ib.js";
/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i=(e,r)=>{const n=t.forwardRef(({color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:c,className:u="",children:l,...s},f)=>{return t.createElement("svg",{ref:f,...o,width:i,height:i,stroke:n,strokeWidth:c?24*Number(a)/Number(i):a,className:["lucide",`lucide-${p=e,p.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,u].join(" "),...s},[...r.map(([e,r])=>t.createElement(e,r)),...Array.isArray(l)?l:[l]]);var p});return n.displayName=`${e}`,n},a=i("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),c=i("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),u=i("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),l=i("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),s=i("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]),f=i("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),p=i("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),h=i("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]),y=i("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),d=i("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),v=i("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),m=i("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),b=i("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),g=i("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),w=i("Cookie",[["path",{d:"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5",key:"laymnq"}],["path",{d:"M8.5 8.5v.01",key:"ue8clq"}],["path",{d:"M16 15.5v.01",key:"14dtrp"}],["path",{d:"M12 12v.01",key:"u5ubse"}],["path",{d:"M11 17v.01",key:"1hyl5a"}],["path",{d:"M7 14v.01",key:"uct60s"}]]),x=i("Copyright",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M14.83 14.83a4 4 0 1 1 0-5.66",key:"1i56pz"}]]),O=i("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),j=i("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]),S=i("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),P=i("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),k=i("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),A=i("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),E=i("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),M=i("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),_=i("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),T=i("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]),C=i("Gavel",[["path",{d:"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8",key:"15492f"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]]),D=i("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),I=i("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),N=i("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),B=i("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),L=i("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),R=i("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]),z=i("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),U=i("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),$=i("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),q=i("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),F=i("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),W=i("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),H=i("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]),V=i("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),X=i("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),G=i("MinusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]),Y=i("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]),K=i("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),Z=i("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),J=i("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Q=i("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),tt=i("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),et=i("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),rt=i("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),nt=i("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ot=i("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),it=i("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),at=i("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),ct=i("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]),ut=i("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),lt=i("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),st=i("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),ft=i("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),pt=i("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),ht=i("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),yt=i("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),dt=i("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),vt=i("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),mt=i("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),bt=i("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]]),gt=i("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),wt=i("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),xt=i("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function Ot(t){var e,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(e=0;e<o;e++)t[e]&&(r=Ot(t[e]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}function jt(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=Ot(t))&&(n&&(n+=" "),n+=e);return n}var St=Array.isArray,Pt="object"==typeof e&&e&&e.Object===Object&&e,kt=Pt,At="object"==typeof self&&self&&self.Object===Object&&self,Et=kt||At||Function("return this")(),Mt=Et.Symbol,_t=Mt,Tt=Object.prototype,Ct=Tt.hasOwnProperty,Dt=Tt.toString,It=_t?_t.toStringTag:void 0;var Nt=function(t){var e=Ct.call(t,It),r=t[It];try{t[It]=void 0;var n=!0}catch(i){}var o=Dt.call(t);return n&&(e?t[It]=r:delete t[It]),o},Bt=Object.prototype.toString;var Lt=Nt,Rt=function(t){return Bt.call(t)},zt=Mt?Mt.toStringTag:void 0;var Ut=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":zt&&zt in Object(t)?Lt(t):Rt(t)};var $t=function(t){return null!=t&&"object"==typeof t},qt=Ut,Ft=$t;var Wt=function(t){return"symbol"==typeof t||Ft(t)&&"[object Symbol]"==qt(t)},Ht=St,Vt=Wt,Xt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Gt=/^\w*$/;var Yt=function(t,e){if(Ht(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!Vt(t))||(Gt.test(t)||!Xt.test(t)||null!=e&&t in Object(e))};var Kt=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)};const Zt=r(Kt);var Jt=Ut,Qt=Kt;var te=function(t){if(!Qt(t))return!1;var e=Jt(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};const ee=r(te);var re,ne=Et["__core-js_shared__"],oe=(re=/[^.]+$/.exec(ne&&ne.keys&&ne.keys.IE_PROTO||""))?"Symbol(src)_1."+re:"";var ie=function(t){return!!oe&&oe in t},ae=Function.prototype.toString;var ce=function(t){if(null!=t){try{return ae.call(t)}catch(e){}try{return t+""}catch(e){}}return""},ue=te,le=ie,se=Kt,fe=ce,pe=/^\[object .+?Constructor\]$/,he=Function.prototype,ye=Object.prototype,de=he.toString,ve=ye.hasOwnProperty,me=RegExp("^"+de.call(ve).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var be=function(t){return!(!se(t)||le(t))&&(ue(t)?me:pe).test(fe(t))},ge=function(t,e){return null==t?void 0:t[e]};var we=function(t,e){var r=ge(t,e);return be(r)?r:void 0},xe=we(Object,"create"),Oe=xe;var je=function(){this.__data__=Oe?Oe(null):{},this.size=0};var Se=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Pe=xe,ke=Object.prototype.hasOwnProperty;var Ae=function(t){var e=this.__data__;if(Pe){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return ke.call(e,t)?e[t]:void 0},Ee=xe,Me=Object.prototype.hasOwnProperty;var _e=xe;var Te=je,Ce=Se,De=Ae,Ie=function(t){var e=this.__data__;return Ee?void 0!==e[t]:Me.call(e,t)},Ne=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=_e&&void 0===e?"__lodash_hash_undefined__":e,this};function Be(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Be.prototype.clear=Te,Be.prototype.delete=Ce,Be.prototype.get=De,Be.prototype.has=Ie,Be.prototype.set=Ne;var Le=Be;var Re=function(){this.__data__=[],this.size=0};var ze=function(t,e){return t===e||t!=t&&e!=e},Ue=ze;var $e=function(t,e){for(var r=t.length;r--;)if(Ue(t[r][0],e))return r;return-1},qe=$e,Fe=Array.prototype.splice;var We=$e;var He=$e;var Ve=$e;var Xe=Re,Ge=function(t){var e=this.__data__,r=qe(e,t);return!(r<0)&&(r==e.length-1?e.pop():Fe.call(e,r,1),--this.size,!0)},Ye=function(t){var e=this.__data__,r=We(e,t);return r<0?void 0:e[r][1]},Ke=function(t){return He(this.__data__,t)>-1},Ze=function(t,e){var r=this.__data__,n=Ve(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this};function Je(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Je.prototype.clear=Xe,Je.prototype.delete=Ge,Je.prototype.get=Ye,Je.prototype.has=Ke,Je.prototype.set=Ze;var Qe=Je,tr=we(Et,"Map"),er=Le,rr=Qe,nr=tr;var or=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t};var ir=function(t,e){var r=t.__data__;return or(e)?r["string"==typeof e?"string":"hash"]:r.map},ar=ir;var cr=ir;var ur=ir;var lr=ir;var sr=function(){this.size=0,this.__data__={hash:new er,map:new(nr||rr),string:new er}},fr=function(t){var e=ar(this,t).delete(t);return this.size-=e?1:0,e},pr=function(t){return cr(this,t).get(t)},hr=function(t){return ur(this,t).has(t)},yr=function(t,e){var r=lr(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this};function dr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}dr.prototype.clear=sr,dr.prototype.delete=fr,dr.prototype.get=pr,dr.prototype.has=hr,dr.prototype.set=yr;var vr=dr,mr=vr;function br(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(br.Cache||mr),r}br.Cache=mr;var gr=br;const wr=r(gr);var xr=gr;var Or=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,jr=/\\(\\)?/g,Sr=function(t){var e=xr(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(Or,function(t,r,n,o){e.push(n?o.replace(jr,"$1"):r||t)}),e});var Pr=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o},kr=Pr,Ar=St,Er=Wt,Mr=Mt?Mt.prototype:void 0,_r=Mr?Mr.toString:void 0;var Tr=function t(e){if("string"==typeof e)return e;if(Ar(e))return kr(e,t)+"";if(Er(e))return _r?_r.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r},Cr=Tr;var Dr=function(t){return null==t?"":Cr(t)},Ir=St,Nr=Yt,Br=Sr,Lr=Dr;var Rr=function(t,e){return Ir(t)?t:Nr(t,e)?[t]:Br(Lr(t))},zr=Wt;var Ur=function(t){if("string"==typeof t||zr(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e},$r=Rr,qr=Ur;var Fr=function(t,e){for(var r=0,n=(e=$r(e,t)).length;null!=t&&r<n;)t=t[qr(e[r++])];return r&&r==n?t:void 0},Wr=Fr;var Hr=function(t,e,r){var n=null==t?void 0:Wr(t,e);return void 0===n?r:n};const Vr=r(Hr);const Xr=r(function(t){return null==t});var Gr=Ut,Yr=St,Kr=$t;const Zr=r(function(t){return"string"==typeof t||!Yr(t)&&Kr(t)&&"[object String]"==Gr(t)});var Jr,Qr={exports:{}},tn={},en=Symbol.for("react.element"),rn=Symbol.for("react.portal"),nn=Symbol.for("react.fragment"),on=Symbol.for("react.strict_mode"),an=Symbol.for("react.profiler"),cn=Symbol.for("react.provider"),un=Symbol.for("react.context"),ln=Symbol.for("react.server_context"),sn=Symbol.for("react.forward_ref"),fn=Symbol.for("react.suspense"),pn=Symbol.for("react.suspense_list"),hn=Symbol.for("react.memo"),yn=Symbol.for("react.lazy"),dn=Symbol.for("react.offscreen");function vn(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case en:switch(t=t.type){case nn:case an:case on:case fn:case pn:return t;default:switch(t=t&&t.$$typeof){case ln:case un:case sn:case yn:case hn:case cn:return t;default:return e}}case rn:return e}}}Jr=Symbol.for("react.module.reference"),tn.ContextConsumer=un,tn.ContextProvider=cn,tn.Element=en,tn.ForwardRef=sn,tn.Fragment=nn,tn.Lazy=yn,tn.Memo=hn,tn.Portal=rn,tn.Profiler=an,tn.StrictMode=on,tn.Suspense=fn,tn.SuspenseList=pn,tn.isAsyncMode=function(){return!1},tn.isConcurrentMode=function(){return!1},tn.isContextConsumer=function(t){return vn(t)===un},tn.isContextProvider=function(t){return vn(t)===cn},tn.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===en},tn.isForwardRef=function(t){return vn(t)===sn},tn.isFragment=function(t){return vn(t)===nn},tn.isLazy=function(t){return vn(t)===yn},tn.isMemo=function(t){return vn(t)===hn},tn.isPortal=function(t){return vn(t)===rn},tn.isProfiler=function(t){return vn(t)===an},tn.isStrictMode=function(t){return vn(t)===on},tn.isSuspense=function(t){return vn(t)===fn},tn.isSuspenseList=function(t){return vn(t)===pn},tn.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===nn||t===an||t===on||t===fn||t===pn||t===dn||"object"==typeof t&&null!==t&&(t.$$typeof===yn||t.$$typeof===hn||t.$$typeof===cn||t.$$typeof===un||t.$$typeof===sn||t.$$typeof===Jr||void 0!==t.getModuleId)},tn.typeOf=vn,Qr.exports=tn;var mn=Qr.exports,bn=Ut,gn=$t;var wn=function(t){return"number"==typeof t||gn(t)&&"[object Number]"==bn(t)};const xn=r(wn);var On=wn;const jn=r(function(t){return On(t)&&t!=+t});var Sn=function(t){return 0===t?0:t>0?1:-1},Pn=function(t){return Zr(t)&&t.indexOf("%")===t.length-1},kn=function(t){return xn(t)&&!jn(t)},An=function(t){return kn(t)||Zr(t)},En=0,Mn=function(t){var e=++En;return"".concat(t||"").concat(e)},_n=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!kn(t)&&!Zr(t))return n;if(Pn(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return jn(r)&&(r=n),o&&r>e&&(r=e),r},Tn=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},Cn=function(t,e){return kn(t)&&kn(e)?function(r){return t+r*(e-t)}:function(){return e}};function Dn(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):Vr(t,e))===r}):null}var In=function(t,e){return kn(t)&&kn(e)?t-e:Zr(t)&&Zr(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))};function Nn(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function Bn(t){return(Bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Ln=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Rn=["points","pathLength"],zn={svg:["viewBox","children"],polygon:Rn,polyline:Rn},Un=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],$n=function(e,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if(t.isValidElement(e)&&(n=e.props),!Zt(n))return null;var o={};return Object.keys(n).forEach(function(t){Un.includes(t)&&(o[t]=r||function(e){return n[t](n,e)})}),o},qn=function(t,e,r){if(!Zt(t)||"object"!==Bn(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];Un.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))}),n},Fn=["children"],Wn=["children"];function Hn(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Vn(t){return(Vn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Xn={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Gn=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},Yn=null,Kn=null,Zn=function e(r){if(r===Yn&&Array.isArray(Kn))return Kn;var n=[];return t.Children.forEach(r,function(t){Xr(t)||(mn.isFragment(t)?n=n.concat(e(t.props.children)):n.push(t))}),Kn=n,Yn=r,n};function Jn(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return Gn(t)}):[Gn(e)],Zn(t).forEach(function(t){var e=Vr(t,"type.displayName")||Vr(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function Qn(t,e){var r=Jn(t,e);return r&&r[0]}var to=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!kn(r)||r<=0||!kn(n)||n<=0)},eo=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ro=function(e,r,n){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var o=e;if(t.isValidElement(e)&&(o=e.props),!Zt(o))return null;var i={};return Object.keys(o).forEach(function(t){var e;(function(t,e,r,n){var o,i=null!==(o=null==zn?void 0:zn[n])&&void 0!==o?o:[];return e.startsWith("data-")||!ee(t)&&(n&&i.includes(e)||Ln.includes(e))||r&&Un.includes(e)})(null===(e=o)||void 0===e?void 0:e[t],t,r,n)&&(i[t]=o[t])}),i},no=function e(r,n){if(r===n)return!0;var o=t.Children.count(r);if(o!==t.Children.count(n))return!1;if(0===o)return!0;if(1===o)return oo(Array.isArray(r)?r[0]:r,Array.isArray(n)?n[0]:n);for(var i=0;i<o;i++){var a=r[i],c=n[i];if(Array.isArray(a)||Array.isArray(c)){if(!e(a,c))return!1}else if(!oo(a,c))return!1}return!0},oo=function(t,e){if(Xr(t)&&Xr(e))return!0;if(!Xr(t)&&!Xr(e)){var r=t.props||{},n=r.children,o=Hn(r,Fn),i=e.props||{},a=i.children,c=Hn(i,Wn);return n&&a?Nn(o,c)&&no(n,a):!n&&!a&&Nn(o,c)}return!1},io=function(t,e){var r=[],n={};return Zn(t).forEach(function(t,o){if(function(t){return t&&t.type&&Zr(t.type)&&eo.indexOf(t.type)>=0}(t))r.push(t);else if(t){var i=Gn(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}}),r},ao=["children","width","height","viewBox","className","style","title","desc"];function co(){return co=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},co.apply(this,arguments)}function uo(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function lo(t){var e=t.children,r=t.width,o=t.height,i=t.viewBox,a=t.className,c=t.style,u=t.title,l=t.desc,s=uo(t,ao),f=i||{width:r,height:o,x:0,y:0},p=jt("recharts-surface",a);return n.createElement("svg",co({},ro(s,!0,"svg"),{className:p,width:r,height:o,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),n.createElement("title",null,u),n.createElement("desc",null,l),e)}var so=["children","className"];function fo(){return fo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fo.apply(this,arguments)}function po(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var ho=n.forwardRef(function(t,e){var r=t.children,o=t.className,i=po(t,so),a=jt("recharts-layer",o);return n.createElement("g",fo({className:a},ro(i,!0),{ref:e}),r)}),yo=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]};var vo=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i};var mo=function(t,e,r){var n=t.length;return r=void 0===r?n:r,!e&&r>=n?t:vo(t,e,r)},bo=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var go=function(t){return bo.test(t)};var wo=function(t){return t.split("")},xo="\\ud800-\\udfff",Oo="["+xo+"]",jo="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",So="\\ud83c[\\udffb-\\udfff]",Po="[^"+xo+"]",ko="(?:\\ud83c[\\udde6-\\uddff]){2}",Ao="[\\ud800-\\udbff][\\udc00-\\udfff]",Eo="(?:"+jo+"|"+So+")"+"?",Mo="[\\ufe0e\\ufe0f]?",_o=Mo+Eo+("(?:\\u200d(?:"+[Po,ko,Ao].join("|")+")"+Mo+Eo+")*"),To="(?:"+[Po+jo+"?",jo,ko,Ao,Oo].join("|")+")",Co=RegExp(So+"(?="+So+")|"+To+_o,"g");var Do=wo,Io=go,No=function(t){return t.match(Co)||[]};var Bo=mo,Lo=go,Ro=function(t){return Io(t)?No(t):Do(t)},zo=Dr;const Uo=r(function(t){return function(e){e=zo(e);var r=Lo(e)?Ro(e):void 0,n=r?r[0]:e.charAt(0),o=r?Bo(r,1).join(""):e.slice(1);return n[t]()+o}}("toUpperCase"));function $o(t){return function(){return t}}const qo=Math.cos,Fo=Math.sin,Wo=Math.sqrt,Ho=Math.PI,Vo=2*Ho,Xo=Math.PI,Go=2*Xo,Yo=1e-6,Ko=Go-Yo;function Zo(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class Jo{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?Zo:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return Zo;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw new Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>Yo)if(Math.abs(s*c-u*l)>Yo&&o){let p=r-i,h=n-a,y=c*c+u*u,d=p*p+h*h,v=Math.sqrt(y),m=Math.sqrt(f),b=o*Math.tan((Xo-Math.acos((y+f-d)/(2*v*m)))/2),g=b/m,w=b/v;Math.abs(g-1)>Yo&&this._append`L${t+g*l},${e+g*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+w*c},${this._y1=e+w*u}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>Yo||Math.abs(this._y1-l)>Yo)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%Go+Go),f>Ko?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>Yo&&this._append`A${r},${r},0,${+(f>=Xo)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function Qo(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError(`invalid digits: ${r}`);e=t}return t},()=>new Jo(e)}function ti(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function ei(t){this._context=t}function ri(t){return new ei(t)}function ni(t){return t[0]}function oi(t){return t[1]}function ii(t,e){var r=$o(!0),n=null,o=ri,i=null,a=Qo(c);function c(c){var u,l,s,f=(c=ti(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?ni:$o(t),e="function"==typeof e?e:void 0===e?oi:$o(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:$o(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:$o(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:$o(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function ai(t,e,r){var n=null,o=$o(!0),i=null,a=ri,c=null,u=Qo(l);function l(l){var s,f,p,h,y,d=(l=ti(l)).length,v=!1,m=new Array(d),b=new Array(d);for(null==i&&(c=a(y=u())),s=0;s<=d;++s){if(!(s<d&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(y)return c=null,y+""||null}function s(){return ii().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?ni:$o(+t),e="function"==typeof e?e:$o(void 0===e?0:+e),r="function"==typeof r?r:void 0===r?oi:$o(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:$o(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:$o(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:$o(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:$o(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:$o(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:$o(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:$o(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}ei.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}};class ci{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}const ui={draw(t,e){const r=Wo(e/Ho);t.moveTo(r,0),t.arc(0,0,r,0,Vo)}},li={draw(t,e){const r=Wo(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},si=Wo(1/3),fi=2*si,pi={draw(t,e){const r=Wo(e/fi),n=r*si;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},hi={draw(t,e){const r=Wo(e),n=-r/2;t.rect(n,n,r,r)}},yi=Fo(Ho/10)/Fo(7*Ho/10),di=Fo(Vo/10)*yi,vi=-qo(Vo/10)*yi,mi={draw(t,e){const r=Wo(.8908130915292852*e),n=di*r,o=vi*r;t.moveTo(0,-r),t.lineTo(n,o);for(let i=1;i<5;++i){const e=Vo*i/5,a=qo(e),c=Fo(e);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},bi=Wo(3),gi={draw(t,e){const r=-Wo(e/(3*bi));t.moveTo(0,2*r),t.lineTo(-bi*r,-r),t.lineTo(bi*r,-r),t.closePath()}},wi=-.5,xi=Wo(3)/2,Oi=1/Wo(12),ji=3*(Oi/2+1),Si={draw(t,e){const r=Wo(e/ji),n=r/2,o=r*Oi,i=n,a=r*Oi+r,c=-i,u=a;t.moveTo(n,o),t.lineTo(i,a),t.lineTo(c,u),t.lineTo(wi*n-xi*o,xi*n+wi*o),t.lineTo(wi*i-xi*a,xi*i+wi*a),t.lineTo(wi*c-xi*u,xi*c+wi*u),t.lineTo(wi*n+xi*o,wi*o-xi*n),t.lineTo(wi*i+xi*a,wi*a-xi*i),t.lineTo(wi*c+xi*u,wi*u-xi*c),t.closePath()}};function Pi(){}function ki(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function Ai(t){this._context=t}function Ei(t){this._context=t}function Mi(t){this._context=t}function _i(t){this._context=t}function Ti(t){return t<0?-1:1}function Ci(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0),c=(i*o+a*n)/(n+o);return(Ti(i)+Ti(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(c))||0}function Di(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function Ii(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function Ni(t){this._context=t}function Bi(t){this._context=new Li(t)}function Li(t){this._context=t}function Ri(t){this._context=t}function zi(t){var e,r,n=t.length-1,o=new Array(n),i=new Array(n),a=new Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[n-1]=(t[n]+o[n-1])/2,e=0;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function Ui(t,e){this._context=t,this._t=e}function $i(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function qi(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function Fi(t,e){return t[e]}function Wi(t){const e=[];return e.key=t,e}function Hi(t){return(Hi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Ai.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:ki(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:ki(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Ei.prototype={areaStart:Pi,areaEnd:Pi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:ki(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Mi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:ki(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},_i.prototype={areaStart:Pi,areaEnd:Pi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},Ni.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Ii(this,this._t0,Di(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,Ii(this,Di(this,r=Ci(this,t,e)),r);break;default:Ii(this,this._t0,r=Ci(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(Bi.prototype=Object.create(Ni.prototype)).point=function(t,e){Ni.prototype.point.call(this,e,t)},Li.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},Ri.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=zi(t),o=zi(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},Ui.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var Vi=["type","size","sizeType"];function Xi(){return Xi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xi.apply(this,arguments)}function Gi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Yi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Gi(Object(r),!0).forEach(function(e){Ki(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Ki(t,e,r){var n;return n=function(t,e){if("object"!=Hi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Hi(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zi(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Ji={symbolCircle:ui,symbolCross:li,symbolDiamond:pi,symbolSquare:hi,symbolStar:mi,symbolTriangle:gi,symbolWye:Si},Qi=Math.PI/180,ta=function(t){var e,r,o=t.type,i=void 0===o?"circle":o,a=t.size,c=void 0===a?64:a,u=t.sizeType,l=void 0===u?"area":u,s=Yi(Yi({},Zi(t,Vi)),{},{type:i,size:c,sizeType:l}),f=s.className,p=s.cx,h=s.cy,y=ro(s,!0);return p===+p&&h===+h&&c===+c?n.createElement("path",Xi({},y,{className:jt("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(h,")"),d:(e=function(t){var e="symbol".concat(Uo(t));return Ji[e]||ui}(i),r=function(t,e){let r=null,n=Qo(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:$o(t||ui),e="function"==typeof e?e:$o(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:$o(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:$o(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o}().type(e).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*Qi;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(c,l,i)),r())})):null};function ea(t){return(ea="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ra(){return ra=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ra.apply(this,arguments)}function na(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function oa(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,sa(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function ia(t,e,r){return e=ca(e),function(t,e){if(e&&("object"===ea(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,aa()?Reflect.construct(e,r||[],ca(t).constructor):e.apply(t,r))}function aa(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(aa=function(){return!!t})()}function ca(t){return(ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ua(t,e){return(ua=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function la(t,e,r){return(e=sa(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sa(t){var e=function(t,e){if("object"!=ea(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ea(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ea(e)?e:e+""}ta.registerSymbol=function(t,e){Ji["symbol".concat(Uo(t))]=e};var fa=32,pa=function(){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),ia(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ua(t,e)}(e,t.PureComponent),oa(e,[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=16,o=fa/6,i=fa/3,a=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:r,x2:fa,y2:r,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(r,"\n            H").concat(fa,"M").concat(2*i,",").concat(r,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(r),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(fa,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?na(Object(r),!0).forEach(function(e){la(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):na(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete c.legendIcon,n.cloneElement(t.legendIcon,c)}return n.createElement(ta,{fill:a,cx:r,cy:r,size:fa,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,i=e.layout,a=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:fa,height:fa},l={display:"horizontal"===i?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||a,f=jt(la(la({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=ee(e.value)?null:e.value;yo(!ee(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return n.createElement("li",ra({className:f,style:l,key:"legend-item-".concat(r)},qn(t.props,e,r)),n.createElement(lo,{width:o,height:o,viewBox:u,style:s},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:h}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?o:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}])}();la(pa,"displayName","Legend"),la(pa,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var ha=Qe;var ya=Qe,da=tr,va=vr;var ma=Qe,ba=function(){this.__data__=new ha,this.size=0},ga=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},wa=function(t){return this.__data__.get(t)},xa=function(t){return this.__data__.has(t)},Oa=function(t,e){var r=this.__data__;if(r instanceof ya){var n=r.__data__;if(!da||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new va(n)}return r.set(t,e),this.size=r.size,this};function ja(t){var e=this.__data__=new ma(t);this.size=e.size}ja.prototype.clear=ba,ja.prototype.delete=ga,ja.prototype.get=wa,ja.prototype.has=xa,ja.prototype.set=Oa;var Sa=ja;var Pa=vr,ka=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},Aa=function(t){return this.__data__.has(t)};function Ea(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new Pa;++e<r;)this.add(t[e])}Ea.prototype.add=Ea.prototype.push=ka,Ea.prototype.has=Aa;var Ma=Ea;var _a=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1};var Ta=function(t,e){return t.has(e)},Ca=Ma,Da=_a,Ia=Ta;var Na=function(t,e,r,n,o,i){var a=1&r,c=t.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var l=i.get(t),s=i.get(e);if(l&&s)return l==e&&s==t;var f=-1,p=!0,h=2&r?new Ca:void 0;for(i.set(t,e),i.set(e,t);++f<c;){var y=t[f],d=e[f];if(n)var v=a?n(d,y,f,e,t,i):n(y,d,f,t,e,i);if(void 0!==v){if(v)continue;p=!1;break}if(h){if(!Da(e,function(t,e){if(!Ia(h,e)&&(y===t||o(y,t,r,n,i)))return h.push(e)})){p=!1;break}}else if(y!==d&&!o(y,d,r,n,i)){p=!1;break}}return i.delete(t),i.delete(e),p};var Ba=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r},La=Et.Uint8Array,Ra=ze,za=Na,Ua=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r},$a=Ba,qa=Mt?Mt.prototype:void 0,Fa=qa?qa.valueOf:void 0;var Wa=function(t,e,r,n,o,i,a){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!i(new La(t),new La(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Ra(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var c=Ua;case"[object Set]":var u=1&n;if(c||(c=$a),t.size!=e.size&&!u)return!1;var l=a.get(t);if(l)return l==e;n|=2,a.set(t,e);var s=za(c(t),c(e),n,o,i,a);return a.delete(t),s;case"[object Symbol]":if(Fa)return Fa.call(t)==Fa.call(e)}return!1};var Ha=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t},Va=Ha,Xa=St;var Ga=function(t,e,r){var n=e(t);return Xa(t)?n:Va(n,r(t))};var Ya=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i},Ka=function(){return[]},Za=Object.prototype.propertyIsEnumerable,Ja=Object.getOwnPropertySymbols,Qa=Ja?function(t){return null==t?[]:(t=Object(t),Ya(Ja(t),function(e){return Za.call(t,e)}))}:Ka;var tc=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n},ec=Ut,rc=$t;var nc,oc,ic,ac,cc,uc,lc,sc,fc=function(t){return rc(t)&&"[object Arguments]"==ec(t)},pc=$t,hc=Object.prototype,yc=hc.hasOwnProperty,dc=hc.propertyIsEnumerable,vc=fc(function(){return arguments}())?fc:function(t){return pc(t)&&yc.call(t,"callee")&&!dc.call(t,"callee")},mc={exports:{}};nc=mc,ic=Et,ac=function(){return!1},cc=(oc=mc.exports)&&!oc.nodeType&&oc,uc=cc&&nc&&!nc.nodeType&&nc,lc=uc&&uc.exports===cc?ic.Buffer:void 0,sc=(lc?lc.isBuffer:void 0)||ac,nc.exports=sc;var bc=mc.exports,gc=/^(?:0|[1-9]\d*)$/;var wc=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&gc.test(t))&&t>-1&&t%1==0&&t<e};var xc=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991},Oc=Ut,jc=xc,Sc=$t,Pc={};Pc["[object Float32Array]"]=Pc["[object Float64Array]"]=Pc["[object Int8Array]"]=Pc["[object Int16Array]"]=Pc["[object Int32Array]"]=Pc["[object Uint8Array]"]=Pc["[object Uint8ClampedArray]"]=Pc["[object Uint16Array]"]=Pc["[object Uint32Array]"]=!0,Pc["[object Arguments]"]=Pc["[object Array]"]=Pc["[object ArrayBuffer]"]=Pc["[object Boolean]"]=Pc["[object DataView]"]=Pc["[object Date]"]=Pc["[object Error]"]=Pc["[object Function]"]=Pc["[object Map]"]=Pc["[object Number]"]=Pc["[object Object]"]=Pc["[object RegExp]"]=Pc["[object Set]"]=Pc["[object String]"]=Pc["[object WeakMap]"]=!1;var kc=function(t){return Sc(t)&&jc(t.length)&&!!Pc[Oc(t)]};var Ac=function(t){return function(e){return t(e)}},Ec={exports:{}};!function(t,e){var r=Pt,n=e&&!e.nodeType&&e,o=n&&t&&!t.nodeType&&t,i=o&&o.exports===n&&r.process,a=function(){try{var t=o&&o.require&&o.require("util").types;return t||i&&i.binding&&i.binding("util")}catch(e){}}();t.exports=a}(Ec,Ec.exports);var Mc=Ec.exports,_c=kc,Tc=Ac,Cc=Mc&&Mc.isTypedArray,Dc=Cc?Tc(Cc):_c,Ic=tc,Nc=vc,Bc=St,Lc=bc,Rc=wc,zc=Dc,Uc=Object.prototype.hasOwnProperty;var $c=function(t,e){var r=Bc(t),n=!r&&Nc(t),o=!r&&!n&&Lc(t),i=!r&&!n&&!o&&zc(t),a=r||n||o||i,c=a?Ic(t.length,String):[],u=c.length;for(var l in t)!e&&!Uc.call(t,l)||a&&("length"==l||o&&("offset"==l||"parent"==l)||i&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||Rc(l,u))||c.push(l);return c},qc=Object.prototype;var Fc=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||qc)};var Wc=function(t,e){return function(r){return t(e(r))}},Hc=Wc(Object.keys,Object),Vc=Fc,Xc=Hc,Gc=Object.prototype.hasOwnProperty;var Yc=te,Kc=xc;var Zc=function(t){return null!=t&&Kc(t.length)&&!Yc(t)},Jc=$c,Qc=function(t){if(!Vc(t))return Xc(t);var e=[];for(var r in Object(t))Gc.call(t,r)&&"constructor"!=r&&e.push(r);return e},tu=Zc;var eu=function(t){return tu(t)?Jc(t):Qc(t)},ru=Ga,nu=Qa,ou=eu;var iu=function(t){return ru(t,ou,nu)},au=Object.prototype.hasOwnProperty;var cu=function(t,e,r,n,o,i){var a=1&r,c=iu(t),u=c.length;if(u!=iu(e).length&&!a)return!1;for(var l=u;l--;){var s=c[l];if(!(a?s in e:au.call(e,s)))return!1}var f=i.get(t),p=i.get(e);if(f&&p)return f==e&&p==t;var h=!0;i.set(t,e),i.set(e,t);for(var y=a;++l<u;){var d=t[s=c[l]],v=e[s];if(n)var m=a?n(v,d,s,e,t,i):n(d,v,s,t,e,i);if(!(void 0===m?d===v||o(d,v,r,n,i):m)){h=!1;break}y||(y="constructor"==s)}if(h&&!y){var b=t.constructor,g=e.constructor;b==g||!("constructor"in t)||!("constructor"in e)||"function"==typeof b&&b instanceof b&&"function"==typeof g&&g instanceof g||(h=!1)}return i.delete(t),i.delete(e),h},uu=we(Et,"DataView"),lu=we(Et,"Promise"),su=we(Et,"Set"),fu=uu,pu=tr,hu=lu,yu=su,du=we(Et,"WeakMap"),vu=Ut,mu=ce,bu="[object Map]",gu="[object Promise]",wu="[object Set]",xu="[object WeakMap]",Ou="[object DataView]",ju=mu(fu),Su=mu(pu),Pu=mu(hu),ku=mu(yu),Au=mu(du),Eu=vu;(fu&&Eu(new fu(new ArrayBuffer(1)))!=Ou||pu&&Eu(new pu)!=bu||hu&&Eu(hu.resolve())!=gu||yu&&Eu(new yu)!=wu||du&&Eu(new du)!=xu)&&(Eu=function(t){var e=vu(t),r="[object Object]"==e?t.constructor:void 0,n=r?mu(r):"";if(n)switch(n){case ju:return Ou;case Su:return bu;case Pu:return gu;case ku:return wu;case Au:return xu}return e});var Mu=Sa,_u=Na,Tu=Wa,Cu=cu,Du=Eu,Iu=St,Nu=bc,Bu=Dc,Lu="[object Arguments]",Ru="[object Array]",zu="[object Object]",Uu=Object.prototype.hasOwnProperty;var $u=function(t,e,r,n,o,i){var a=Iu(t),c=Iu(e),u=a?Ru:Du(t),l=c?Ru:Du(e),s=(u=u==Lu?zu:u)==zu,f=(l=l==Lu?zu:l)==zu,p=u==l;if(p&&Nu(t)){if(!Nu(e))return!1;a=!0,s=!1}if(p&&!s)return i||(i=new Mu),a||Bu(t)?_u(t,e,r,n,o,i):Tu(t,e,u,r,n,o,i);if(!(1&r)){var h=s&&Uu.call(t,"__wrapped__"),y=f&&Uu.call(e,"__wrapped__");if(h||y){var d=h?t.value():t,v=y?e.value():e;return i||(i=new Mu),o(d,v,r,n,i)}}return!!p&&(i||(i=new Mu),Cu(t,e,r,n,o,i))},qu=$t;var Fu=function t(e,r,n,o,i){return e===r||(null==e||null==r||!qu(e)&&!qu(r)?e!=e&&r!=r:$u(e,r,n,o,t,i))},Wu=Sa,Hu=Fu;var Vu=Kt;var Xu=function(t){return t==t&&!Vu(t)},Gu=Xu,Yu=eu;var Ku=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}},Zu=function(t,e,r,n){var o=r.length,i=o,a=!n;if(null==t)return!i;for(t=Object(t);o--;){var c=r[o];if(a&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++o<i;){var u=(c=r[o])[0],l=t[u],s=c[1];if(a&&c[2]){if(void 0===l&&!(u in t))return!1}else{var f=new Wu;if(n)var p=n(l,s,u,t,e,f);if(!(void 0===p?Hu(s,l,3,n,f):p))return!1}}return!0},Ju=function(t){for(var e=Yu(t),r=e.length;r--;){var n=e[r],o=t[n];e[r]=[n,o,Gu(o)]}return e},Qu=Ku;var tl=Rr,el=vc,rl=St,nl=wc,ol=xc,il=Ur;var al=function(t,e){return null!=t&&e in Object(t)},cl=function(t,e,r){for(var n=-1,o=(e=tl(e,t)).length,i=!1;++n<o;){var a=il(e[n]);if(!(i=null!=t&&r(t,a)))break;t=t[a]}return i||++n!=o?i:!!(o=null==t?0:t.length)&&ol(o)&&nl(a,o)&&(rl(t)||el(t))};var ul=Fu,ll=Hr,sl=function(t,e){return null!=t&&cl(t,e,al)},fl=Yt,pl=Xu,hl=Ku,yl=Ur;var dl=function(t){return t};var vl=Fr;var ml=function(t){return function(e){return null==e?void 0:e[t]}},bl=function(t){return function(e){return vl(e,t)}},gl=Yt,wl=Ur;var xl=function(t){var e=Ju(t);return 1==e.length&&e[0][2]?Qu(e[0][0],e[0][1]):function(r){return r===t||Zu(r,t,e)}},Ol=function(t,e){return fl(t)&&pl(e)?hl(yl(t),e):function(r){var n=ll(r,t);return void 0===n&&n===e?sl(r,t):ul(e,n,3)}},jl=dl,Sl=St,Pl=function(t){return gl(t)?ml(wl(t)):bl(t)};var kl=function(t){return"function"==typeof t?t:null==t?jl:"object"==typeof t?Sl(t)?Ol(t[0],t[1]):xl(t):Pl(t)};var Al=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1};var El=Al,Ml=function(t){return t!=t},_l=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1};var Tl=function(t,e,r){return e==e?_l(t,e,r):El(t,Ml,r)};var Cl=function(t,e){return!!(null==t?0:t.length)&&Tl(t,e,0)>-1};var Dl=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1};var Il=su,Nl=function(){},Bl=Il&&1/Ba(new Il([,-0]))[1]==1/0?function(t){return new Il(t)}:Nl,Ll=Ma,Rl=Cl,zl=Dl,Ul=Ta,$l=Bl,ql=Ba;var Fl=kl,Wl=function(t,e,r){var n=-1,o=Rl,i=t.length,a=!0,c=[],u=c;if(r)a=!1,o=zl;else if(i>=200){var l=e?null:$l(t);if(l)return ql(l);a=!1,o=Ul,u=new Ll}else u=e?[]:c;t:for(;++n<i;){var s=t[n],f=e?e(s):s;if(s=r||0!==s?s:0,a&&f==f){for(var p=u.length;p--;)if(u[p]===f)continue t;e&&u.push(f),c.push(s)}else o(u,f,r)||(u!==c&&u.push(f),c.push(s))}return c};const Hl=r(function(t,e){return t&&t.length?Wl(t,Fl(e)):[]});function Vl(t,e,r){return!0===e?Hl(t,r):ee(e)?Hl(t,e):t}function Xl(t){return(Xl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Gl=["ref"];function Yl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Kl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yl(Object(r),!0).forEach(function(e){rs(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Zl(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ns(n.key),n)}}function Jl(t,e,r){return e=ts(e),function(t,e){if(e&&("object"===Xl(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ql()?Reflect.construct(e,r||[],ts(t).constructor):e.apply(t,r))}function Ql(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Ql=function(){return!!t})()}function ts(t){return(ts=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function es(t,e){return(es=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rs(t,e,r){return(e=ns(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ns(t){var e=function(t,e){if("object"!=Xl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Xl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Xl(e)?e:e+""}function os(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function is(t){return t.value}var as=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return rs(t=Jl(this,e,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&es(t,e)}(e,t.PureComponent),r=e,i=[{key:"getWithHeight",value:function(t,e){var r=Kl(Kl({},this.defaultProps),t.props).layout;return"vertical"===r&&kn(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(o=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Kl({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),Kl(Kl({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=Kl(Kl({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=os(e,Gl);return n.createElement(pa,r)}(r,Kl(Kl({},this.props),{},{payload:Vl(u,c,is)})))}}])&&Zl(r.prototype,o),i&&Zl(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();rs(as,"displayName","Legend"),rs(as,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var cs=vc,us=St,ls=Mt?Mt.isConcatSpreadable:void 0;var ss=Ha,fs=function(t){return us(t)||cs(t)||!!(ls&&t&&t[ls])};var ps=function t(e,r,n,o,i){var a=-1,c=e.length;for(n||(n=fs),i||(i=[]);++a<c;){var u=e[a];r>0&&n(u)?r>1?t(u,r-1,n,o,i):ss(i,u):o||(i[i.length]=u)}return i};var hs=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}(),ys=eu;var ds=function(t,e){return t&&hs(t,e,ys)},vs=Zc;var ms=function(t,e){return function(r,n){if(null==r)return r;if(!vs(r))return t(r,n);for(var o=r.length,i=e?o:-1,a=Object(r);(e?i--:++i<o)&&!1!==n(a[i],i,a););return r}}(ds),bs=ms,gs=Zc;var ws=function(t,e){var r=-1,n=gs(t)?Array(t.length):[];return bs(t,function(t,o,i){n[++r]=e(t,o,i)}),n};var xs=Wt;var Os=function(t,e){if(t!==e){var r=void 0!==t,n=null===t,o=t==t,i=xs(t),a=void 0!==e,c=null===e,u=e==e,l=xs(e);if(!c&&!l&&!i&&t>e||i&&a&&u&&!c&&!l||n&&a&&u||!r&&u||!o)return 1;if(!n&&!i&&!l&&t<e||l&&r&&o&&!n&&!i||c&&r&&o||!a&&o||!u)return-1}return 0};var js=Pr,Ss=Fr,Ps=kl,ks=ws,As=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t},Es=Ac,Ms=function(t,e,r){for(var n=-1,o=t.criteria,i=e.criteria,a=o.length,c=r.length;++n<a;){var u=Os(o[n],i[n]);if(u)return n>=c?u:u*("desc"==r[n]?-1:1)}return t.index-e.index},_s=dl,Ts=St;var Cs=function(t,e,r){e=e.length?js(e,function(t){return Ts(t)?function(e){return Ss(e,1===t.length?t[0]:t)}:t}):[_s];var n=-1;e=js(e,Es(Ps));var o=ks(t,function(t,r,o){return{criteria:js(e,function(e){return e(t)}),index:++n,value:t}});return As(o,function(t,e){return Ms(t,e,r)})};var Ds=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)},Is=Math.max;var Ns=function(t,e,r){return e=Is(void 0===e?t.length-1:e,0),function(){for(var n=arguments,o=-1,i=Is(n.length-e,0),a=Array(i);++o<i;)a[o]=n[e+o];o=-1;for(var c=Array(e+1);++o<e;)c[o]=n[o];return c[e]=r(a),Ds(t,this,c)}};var Bs=function(t){return function(){return t}},Ls=we,Rs=function(){try{var t=Ls(Object,"defineProperty");return t({},"",{}),t}catch(e){}}(),zs=Bs,Us=Rs,$s=Us?function(t,e){return Us(t,"toString",{configurable:!0,enumerable:!1,value:zs(e),writable:!0})}:dl,qs=Date.now;var Fs=function(t){var e=0,r=0;return function(){var n=qs(),o=16-(n-r);if(r=n,o>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}($s),Ws=dl,Hs=Ns,Vs=Fs;var Xs=ze,Gs=Zc,Ys=wc,Ks=Kt;var Zs=function(t,e,r){if(!Ks(r))return!1;var n=typeof e;return!!("number"==n?Gs(r)&&Ys(e,r.length):"string"==n&&e in r)&&Xs(r[e],t)},Js=ps,Qs=Cs,tf=Zs;const ef=r(function(t,e){return Vs(Hs(t,e,Ws),t+"")}(function(t,e){if(null==t)return[];var r=e.length;return r>1&&tf(t,e[0],e[1])?e=[]:r>2&&tf(e[0],e[1],e[2])&&(e=[e[0]]),Qs(t,Js(e,1),[])}));function rf(t){return(rf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nf(){return nf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},nf.apply(this,arguments)}function of(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return af(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return af(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function af(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function cf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cf(Object(r),!0).forEach(function(e){lf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lf(t,e,r){var n;return n=function(t,e){if("object"!=rf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==rf(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sf(t){return Array.isArray(t)&&An(t[0])&&An(t[1])?t.join(" ~ "):t}var ff=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,i=void 0===o?{}:o,a=t.itemStyle,c=void 0===a?{}:a,u=t.labelStyle,l=void 0===u?{}:u,s=t.payload,f=t.formatter,p=t.itemSorter,h=t.wrapperClassName,y=t.labelClassName,d=t.label,v=t.labelFormatter,m=t.accessibilityLayer,b=void 0!==m&&m,g=uf({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),w=uf({margin:0},l),x=!Xr(d),O=x?d:"",j=jt("recharts-default-tooltip",h),S=jt("recharts-tooltip-label",y);x&&v&&null!=s&&(O=v(d,s));var P=b?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",nf({className:j,style:g},P),n.createElement("p",{className:S,style:w},n.isValidElement(O)?O:"".concat(O)),function(){if(s&&s.length){var t=(p?ef(s,p):s).map(function(t,e){if("none"===t.type)return null;var o=uf({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},c),i=t.formatter||f||sf,a=t.value,u=t.name,l=a,p=u;if(i&&null!=l&&null!=p){var h=i(a,u,t,e,s);if(Array.isArray(h)){var y=of(h,2);l=y[0],p=y[1]}else l=h}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},An(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,An(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},l),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function pf(t){return(pf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hf(t,e,r){var n;return n=function(t,e){if("object"!=pf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==pf(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var yf="recharts-tooltip-wrapper",df={visibility:"hidden"};function vf(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return jt(yf,hf(hf(hf(hf({},"".concat(yf,"-right"),kn(r)&&e&&kn(e.x)&&r>=e.x),"".concat(yf,"-left"),kn(r)&&e&&kn(e.x)&&r<e.x),"".concat(yf,"-bottom"),kn(n)&&e&&kn(e.y)&&n>=e.y),"".concat(yf,"-top"),kn(n)&&e&&kn(e.y)&&n<e.y))}function mf(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&kn(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function bf(t){return(bf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function gf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function wf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?gf(Object(r),!0).forEach(function(e){kf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):gf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function xf(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Af(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Of(t,e,r){return e=Sf(e),function(t,e){if(e&&("object"===bf(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,jf()?Reflect.construct(e,r||[],Sf(t).constructor):e.apply(t,r))}function jf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(jf=function(){return!!t})()}function Sf(t){return(Sf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Pf(t,e){return(Pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function kf(t,e,r){return(e=Af(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Af(t){var e=function(t,e){if("object"!=bf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=bf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==bf(e)?e:e+""}var Ef=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return kf(t=Of(this,e,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),kf(t,"handleKeyDown",function(e){var r,n,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pf(t,e)}(e,t.PureComponent),xf(e,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.children,u=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,y=e.useTranslate3d,d=e.viewBox,v=e.wrapperStyle,m=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,c=t.reverseDirection,u=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:u.height>0&&u.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=mf({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.width,viewBox:s,viewBoxDimension:s.width}),translateY:r=mf({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):df,cssClasses:vf({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:u,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:d}),b=m.cssClasses,g=m.cssProperties,w=wf(wf({transition:s&&r?"transform ".concat(i,"ms ").concat(a):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return n.createElement("div",{tabIndex:-1,className:b,style:w,ref:function(e){t.wrapperNode=e}},c)}}])}(),Mf={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return Mf[t]},set:function(t,e){if("string"==typeof t)Mf[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){Mf[e]=t[e]})}}};function _f(t){return(_f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Tf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Cf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Tf(Object(r),!0).forEach(function(e){Rf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Tf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Df(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,zf(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function If(t,e,r){return e=Bf(e),function(t,e){if(e&&("object"===_f(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Nf()?Reflect.construct(e,r||[],Bf(t).constructor):e.apply(t,r))}function Nf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Nf=function(){return!!t})()}function Bf(t){return(Bf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Lf(t,e){return(Lf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Rf(t,e,r){return(e=zf(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function zf(t){var e=function(t,e){if("object"!=_f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_f(e)?e:e+""}function Uf(t){return t.dataKey}var $f=function(){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),If(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Lf(t,e)}(e,t.PureComponent),Df(e,[{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.content,u=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,y=e.position,d=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,b=e.wrapperStyle,g=null!=p?p:[];l&&g.length&&(g=Vl(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,Uf));var w=g.length>0;return n.createElement(Ef,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:r,coordinate:u,hasPayload:w,offset:f,position:y,reverseDirection:d,useTranslate3d:v,viewBox:m,wrapperStyle:b},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):"function"==typeof t?n.createElement(t,e):n.createElement(ff,e)}(c,Cf(Cf({},this.props),{},{payload:g})))}}])}();Rf($f,"displayName","Tooltip"),Rf($f,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Mf.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var qf=Et,Ff=/\s/;var Wf=function(t){for(var e=t.length;e--&&Ff.test(t.charAt(e)););return e},Hf=/^\s+/;var Vf=function(t){return t?t.slice(0,Wf(t)+1).replace(Hf,""):t},Xf=Kt,Gf=Wt,Yf=/^[-+]0x[0-9a-f]+$/i,Kf=/^0b[01]+$/i,Zf=/^0o[0-7]+$/i,Jf=parseInt;var Qf=function(t){if("number"==typeof t)return t;if(Gf(t))return NaN;if(Xf(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Xf(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=Vf(t);var r=Kf.test(t);return r||Zf.test(t)?Jf(t.slice(2),r?2:8):Yf.test(t)?NaN:+t},tp=Kt,ep=function(){return qf.Date.now()},rp=Qf,np=Math.max,op=Math.min;var ip=function(t,e,r){var n,o,i,a,c,u,l=0,s=!1,f=!1,p=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function h(e){var r=n,i=o;return n=o=void 0,l=e,a=t.apply(i,r)}function y(t){var r=t-u;return void 0===u||r>=e||r<0||f&&t-l>=i}function d(){var t=ep();if(y(t))return v(t);c=setTimeout(d,function(t){var r=e-(t-u);return f?op(r,i-(t-l)):r}(t))}function v(t){return c=void 0,p&&n?h(t):(n=o=void 0,a)}function m(){var t=ep(),r=y(t);if(n=arguments,o=this,u=t,r){if(void 0===c)return function(t){return l=t,c=setTimeout(d,e),s?h(t):a}(u);if(f)return clearTimeout(c),c=setTimeout(d,e),h(u)}return void 0===c&&(c=setTimeout(d,e)),a}return e=rp(e)||0,tp(r)&&(s=!!r.leading,i=(f="maxWait"in r)?np(rp(r.maxWait)||0,e):i,p="trailing"in r?!!r.trailing:p),m.cancel=function(){void 0!==c&&clearTimeout(c),l=0,n=u=o=c=void 0},m.flush=function(){return void 0===c?a:v(ep())},m},ap=Kt;const cp=r(function(t,e,r){var n=!0,o=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return ap(r)&&(n="leading"in r?!!r.leading:n,o="trailing"in r?!!r.trailing:o),ip(t,e,{leading:n,maxWait:e,trailing:o})});function up(t){return(up="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lp(Object(r),!0).forEach(function(e){fp(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fp(t,e,r){var n;return n=function(t,e){if("object"!=up(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=up(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==up(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return hp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hp(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var yp=t.forwardRef(function(e,r){var o=e.aspect,i=e.initialDimension,a=void 0===i?{width:-1,height:-1}:i,c=e.width,u=void 0===c?"100%":c,l=e.height,s=void 0===l?"100%":l,f=e.minWidth,p=void 0===f?0:f,h=e.minHeight,y=e.maxHeight,d=e.children,v=e.debounce,m=void 0===v?0:v,b=e.id,g=e.className,w=e.onResize,x=e.style,O=void 0===x?{}:x,j=t.useRef(null),S=t.useRef();S.current=w,t.useImperativeHandle(r,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var P=pp(t.useState({containerWidth:a.width,containerHeight:a.height}),2),k=P[0],A=P[1],E=t.useCallback(function(t,e){A(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);t.useEffect(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;E(n,o),null===(e=S.current)||void 0===e||e.call(S,n,o)};m>0&&(t=cp(t,m,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect(),n=r.width,o=r.height;return E(n,o),e.observe(j.current),function(){e.disconnect()}},[E,m]);var M=t.useMemo(function(){var e=k.containerWidth,r=k.containerHeight;if(e<0||r<0)return null;yo(Pn(u)||Pn(s),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,s),yo(!o||o>0,"The aspect(%s) must be greater than zero.",o);var i=Pn(u)?e:u,a=Pn(s)?r:s;o&&o>0&&(i?a=i/o:a&&(i=a*o),y&&a>y&&(a=y)),yo(i>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",i,a,u,s,p,h,o);var c=!Array.isArray(d)&&Gn(d.type).endsWith("Chart");return n.Children.map(d,function(e){return n.isValidElement(e)?t.cloneElement(e,sp({width:i,height:a},c?{style:sp({height:"100%",width:"100%",maxHeight:a,maxWidth:i},e.props.style)}:{})):e})},[o,d,s,y,h,p,k,u]);return n.createElement("div",{id:b?"".concat(b):void 0,className:jt("recharts-responsive-container",g),style:sp(sp({},O),{},{width:u,height:s,minWidth:p,minHeight:h,maxHeight:y}),ref:j},M)}),dp=function(t){return null};function vp(t){return(vp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function mp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function bp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?mp(Object(r),!0).forEach(function(e){gp(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function gp(t,e,r){var n;return n=function(t,e){if("object"!=vp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=vp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==vp(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}dp.displayName="Cell";var wp={widthCache:{},cacheCount:0},xp={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Op="recharts_measurement_span";var jp=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||Mf.isSsr)return{width:0,height:0};var r,n=(r=bp({},e),Object.keys(r).forEach(function(t){r[t]||delete r[t]}),r),o=JSON.stringify({text:t,copyStyle:n});if(wp.widthCache[o])return wp.widthCache[o];try{var i=document.getElementById(Op);i||((i=document.createElement("span")).setAttribute("id",Op),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=bp(bp({},xp),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return wp.widthCache[o]=u,++wp.cacheCount>2e3&&(wp.cacheCount=0,wp.widthCache={}),u}catch(l){return{width:0,height:0}}};function Sp(t){return(Sp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Pp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return kp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kp(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ap(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ep(n.key),n)}}function Ep(t){var e=function(t,e){if("object"!=Sp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Sp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==Sp(e)?e:e+""}var Mp=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,_p=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Tp=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Cp=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Dp={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Ip=Object.keys(Dp),Np="NaN";var Bp=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||Tp.test(r)||(this.num=NaN,this.unit=""),Ip.includes(r)&&(this.num=function(t,e){return t*Dp[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=Pp(null!==(r=Cp.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!=i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&Ap(e.prototype,r),n&&Ap(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function Lp(t){if(t.includes(Np))return Np;for(var e=t;e.includes("*")||e.includes("/");){var r,n=Pp(null!==(r=Mp.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=Bp.parse(null!=o?o:""),u=Bp.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return Np;e=e.replace(Mp,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=Pp(null!==(s=_p.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],y=f[3],d=Bp.parse(null!=p?p:""),v=Bp.parse(null!=y?y:""),m="+"===h?d.add(v):d.subtract(v);if(m.isNaN())return Np;e=e.replace(_p,m.toString())}return e}var Rp=/\(([^()]*)\)/;function zp(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=Pp(Rp.exec(e),2)[1];e=e.replace(Rp,Lp(r))}return e}(e),e=Lp(e)}function Up(t){var e=function(t){try{return zp(t)}catch(e){return Np}}(t.slice(5,-1));return e===Np?"":e}var $p=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],qp=["dx","dy","angle","className","breakAll"];function Fp(){return Fp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Fp.apply(this,arguments)}function Wp(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Hp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Vp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Vp(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Vp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Xp=/[ \f\n\r\t\v\u2028\u2029]+/,Gp=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return Xr(e)||(o=r?e.toString().split(""):e.toString().split(Xp)),{wordsWithComputedWidth:o.map(function(t){return{word:t,width:jp(t,n).width}}),spaceWidth:r?0:jp(" ",n).width}}catch(i){return null}},Yp=function(t){return[{words:Xr(t)?[]:t.toString().split(Xp)}]},Kp=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!Mf.isSsr){var c=Gp({breakAll:i,children:n,style:o});return c?function(t,e,r,n,o){var i=t.maxLines,a=t.children,c=t.style,u=t.breakAll,l=kn(i),s=a,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||o||c.width+a+r<Number(n)))c.words.push(i),c.width+=a+r;else{var u={words:[i],width:a};t.push(u)}return t},[])},p=f(e);if(!l)return p;for(var h,y=function(t){var e=s.slice(0,t),r=Gp({breakAll:u,style:c,children:e+"…"}).wordsWithComputedWidth,o=f(r),a=o.length>i||function(t){return t.reduce(function(t,e){return t.width>e.width?t:e})}(o).width>Number(n);return[a,o]},d=0,v=s.length-1,m=0;d<=v&&m<=s.length-1;){var b=Math.floor((d+v)/2),g=Hp(y(b-1),2),w=g[0],x=g[1],O=Hp(y(b),1)[0];if(w||O||(d=b+1),w&&O&&(v=b-1),!w&&O){h=x;break}m++}return h||p}({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,e,r):Yp(n)}return Yp(n)},Zp="#808080",Jp=function(e){var r=e.x,o=void 0===r?0:r,i=e.y,a=void 0===i?0:i,c=e.lineHeight,u=void 0===c?"1em":c,l=e.capHeight,s=void 0===l?"0.71em":l,f=e.scaleToFit,p=void 0!==f&&f,h=e.textAnchor,y=void 0===h?"start":h,d=e.verticalAnchor,v=void 0===d?"end":d,m=e.fill,b=void 0===m?Zp:m,g=Wp(e,$p),w=t.useMemo(function(){return Kp({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:p,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,p,g.style,g.width]),x=g.dx,O=g.dy,j=g.angle,S=g.className,P=g.breakAll,k=Wp(g,qp);if(!An(o)||!An(a))return null;var A,E=o+(kn(x)?x:0),M=a+(kn(O)?O:0);switch(v){case"start":A=Up("calc(".concat(s,")"));break;case"middle":A=Up("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:A=Up("calc(".concat(w.length-1," * -").concat(u,")"))}var _=[];if(p){var T=w[0].width,C=g.width;_.push("scale(".concat((kn(C)?C/T:1)/T,")"))}return j&&_.push("rotate(".concat(j,", ").concat(E,", ").concat(M,")")),_.length&&(k.transform=_.join(" ")),n.createElement("text",Fp({},ro(k,!0),{x:E,y:M,className:jt("recharts-text",S),textAnchor:y,fill:b.includes("url")?Zp:b}),w.map(function(t,e){var r=t.words.join(P?"":" ");return n.createElement("tspan",{x:E,dy:0===e?A:u,key:"".concat(r,"-").concat(e)},r)}))};function Qp(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function th(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function eh(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=Qp,r=(e,r)=>Qp(t(e),r),n=(e,r)=>t(e)-r):(e=t===Qp||t===th?t:rh,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){const a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<=0?o=e+1:i=e}while(o<i)}return o}}}function rh(){return 0}function nh(t){return null===t?NaN:+t}const oh=eh(Qp).right;eh(nh).center;class ih extends Map{constructor(t,e=ch){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[r,n]of t)this.set(r,n)}get(t){return super.get(ah(this,t))}has(t){return super.has(ah(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){const n=e(r);t.has(n)&&(r=t.get(n),t.delete(n));return r}(this,t))}}function ah({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):r}function ch(t){return null!==t&&"object"==typeof t?t.valueOf():t}function uh(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}const lh=Math.sqrt(50),sh=Math.sqrt(10),fh=Math.sqrt(2);function ph(t,e,r){const n=(e-t)/Math.max(0,r),o=Math.floor(Math.log10(n)),i=n/Math.pow(10,o),a=i>=lh?10:i>=sh?5:i>=fh?2:1;let c,u,l;return o<0?(l=Math.pow(10,-o)/a,c=Math.round(t*l),u=Math.round(e*l),c/l<t&&++c,u/l>e&&--u,l=-l):(l=Math.pow(10,o)*a,c=Math.round(t/l),u=Math.round(e/l),c*l<t&&++c,u*l>e&&--u),u<c&&.5<=r&&r<2?ph(t,e,2*r):[c,u,l]}function hh(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[o,i,a]=n?ph(e,t,r):ph(t,e,r);if(!(i>=o))return[];const c=i-o+1,u=new Array(c);if(n)if(a<0)for(let l=0;l<c;++l)u[l]=(i-l)/-a;else for(let l=0;l<c;++l)u[l]=(i-l)*a;else if(a<0)for(let l=0;l<c;++l)u[l]=(o+l)/-a;else for(let l=0;l<c;++l)u[l]=(o+l)*a;return u}function yh(t,e,r){return ph(t=+t,e=+e,r=+r)[2]}function dh(t,e,r){r=+r;const n=(e=+e)<(t=+t),o=n?yh(e,t,r):yh(t,e,r);return(n?-1:1)*(o<0?1/-o:o)}function vh(t,e){let r;for(const n of t)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);return r}function mh(t,e){let r;for(const n of t)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);return r}function bh(t,e,r=0,n=1/0,o){if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(o=void 0===o?uh:function(t=Qp){if(t===Qp)return uh;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);n>r;){if(n-r>600){const i=n-r+1,a=e-r+1,c=Math.log(i),u=.5*Math.exp(2*c/3),l=.5*Math.sqrt(c*u*(i-u)/i)*(a-i/2<0?-1:1);bh(t,e,Math.max(r,Math.floor(e-a*u/i+l)),Math.min(n,Math.floor(e+(i-a)*u/i+l)),o)}const i=t[e];let a=r,c=n;for(gh(t,r,e),o(t[n],i)>0&&gh(t,r,n);a<c;){for(gh(t,a,c),++a,--c;o(t[a],i)<0;)++a;for(;o(t[c],i)>0;)--c}0===o(t[r],i)?gh(t,r,c):(++c,gh(t,c,n)),c<=e&&(r=c+1),e<=c&&(n=c-1)}return t}function gh(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function wh(t,e,r=nh){if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}function xh(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function Oh(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}const jh=Symbol("implicit");function Sh(){var t=new ih,e=[],r=[],n=jh;function o(o){let i=t.get(o);if(void 0===i){if(n!==jh)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new ih;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return Sh(e,r).unknown(n)},xh.apply(o,arguments),o}function Ph(){var t,e,r=Sh().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var y=function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=new Array(o);++n<o;)i[n]=t+n*r;return i}(r).map(function(e){return p+t*e});return o(f?y.reverse():y)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return Ph(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},xh.apply(f(),arguments)}function kh(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return kh(e())},t}function Ah(){return kh(Ph.apply(null,arguments).paddingInner(1))}function Eh(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function Mh(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function _h(){}var Th=.7,Ch=1/Th,Dh="\\s*([+-]?\\d+)\\s*",Ih="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Nh="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Bh=/^#([0-9a-f]{3,8})$/,Lh=new RegExp(`^rgb\\(${Dh},${Dh},${Dh}\\)$`),Rh=new RegExp(`^rgb\\(${Nh},${Nh},${Nh}\\)$`),zh=new RegExp(`^rgba\\(${Dh},${Dh},${Dh},${Ih}\\)$`),Uh=new RegExp(`^rgba\\(${Nh},${Nh},${Nh},${Ih}\\)$`),$h=new RegExp(`^hsl\\(${Ih},${Nh},${Nh}\\)$`),qh=new RegExp(`^hsla\\(${Ih},${Nh},${Nh},${Ih}\\)$`),Fh={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Wh(){return this.rgb().formatHex()}function Hh(){return this.rgb().formatRgb()}function Vh(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=Bh.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?Xh(e):3===r?new Kh(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?Gh(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?Gh(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=Lh.exec(t))?new Kh(e[1],e[2],e[3],1):(e=Rh.exec(t))?new Kh(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=zh.exec(t))?Gh(e[1],e[2],e[3],e[4]):(e=Uh.exec(t))?Gh(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=$h.exec(t))?ry(e[1],e[2]/100,e[3]/100,1):(e=qh.exec(t))?ry(e[1],e[2]/100,e[3]/100,e[4]):Fh.hasOwnProperty(t)?Xh(Fh[t]):"transparent"===t?new Kh(NaN,NaN,NaN,0):null}function Xh(t){return new Kh(t>>16&255,t>>8&255,255&t,1)}function Gh(t,e,r,n){return n<=0&&(t=e=r=NaN),new Kh(t,e,r,n)}function Yh(t,e,r,n){return 1===arguments.length?((o=t)instanceof _h||(o=Vh(o)),o?new Kh((o=o.rgb()).r,o.g,o.b,o.opacity):new Kh):new Kh(t,e,r,null==n?1:n);var o}function Kh(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function Zh(){return`#${ey(this.r)}${ey(this.g)}${ey(this.b)}`}function Jh(){const t=Qh(this.opacity);return`${1===t?"rgb(":"rgba("}${ty(this.r)}, ${ty(this.g)}, ${ty(this.b)}${1===t?")":`, ${t})`}`}function Qh(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ty(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function ey(t){return((t=ty(t))<16?"0":"")+t.toString(16)}function ry(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oy(t,e,r,n)}function ny(t){if(t instanceof oy)return new oy(t.h,t.s,t.l,t.opacity);if(t instanceof _h||(t=Vh(t)),!t)return new oy;if(t instanceof oy)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+6*(r<n):r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oy(a,c,u,t.opacity)}function oy(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function iy(t){return(t=(t||0)%360)<0?t+360:t}function ay(t){return Math.max(0,Math.min(1,t||0))}function cy(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}Eh(_h,Vh,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Wh,formatHex:Wh,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ny(this).formatHsl()},formatRgb:Hh,toString:Hh}),Eh(Kh,Yh,Mh(_h,{brighter(t){return t=null==t?Ch:Math.pow(Ch,t),new Kh(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Th:Math.pow(Th,t),new Kh(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Kh(ty(this.r),ty(this.g),ty(this.b),Qh(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Zh,formatHex:Zh,formatHex8:function(){return`#${ey(this.r)}${ey(this.g)}${ey(this.b)}${ey(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Jh,toString:Jh})),Eh(oy,function(t,e,r,n){return 1===arguments.length?ny(t):new oy(t,e,r,null==n?1:n)},Mh(_h,{brighter(t){return t=null==t?Ch:Math.pow(Ch,t),new oy(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Th:Math.pow(Th,t),new oy(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new Kh(cy(t>=240?t-240:t+120,o,n),cy(t,o,n),cy(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oy(iy(this.h),ay(this.s),ay(this.l),Qh(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Qh(this.opacity);return`${1===t?"hsl(":"hsla("}${iy(this.h)}, ${100*ay(this.s)}%, ${100*ay(this.l)}%${1===t?")":`, ${t})`}`}}));const uy=t=>()=>t;function ly(t){return 1===(t=+t)?sy:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):uy(isNaN(e)?r:e)}}function sy(t,e){var r=e-t;return r?function(t,e){return function(r){return t+r*e}}(t,r):uy(isNaN(t)?e:t)}const fy=function t(e){var r=ly(e);function n(t,e){var n=r((t=Yh(t)).r,(e=Yh(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=sy(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function py(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}}function hy(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=new Array(o),a=new Array(n);for(r=0;r<o;++r)i[r]=wy(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}function yy(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function dy(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function vy(t,e){var r,n={},o={};for(r in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)r in t?n[r]=wy(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}var my=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,by=new RegExp(my.source,"g");function gy(t,e){var r,n,o,i=my.lastIndex=by.lastIndex=0,a=-1,c=[],u=[];for(t+="",e+="";(r=my.exec(t))&&(n=by.exec(e));)(o=n.index)>i&&(o=e.slice(i,o),c[a]?c[a]+=o:c[++a]=o),(r=r[0])===(n=n[0])?c[a]?c[a]+=n:c[++a]=n:(c[++a]=null,u.push({i:a,x:dy(r,n)})),i=by.lastIndex;return i<e.length&&(o=e.slice(i),c[a]?c[a]+=o:c[++a]=o),c.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var r,n=0;n<e;++n)c[(r=u[n]).i]=r.x(t);return c.join("")})}function wy(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?uy(e):("number"===o?dy:"string"===o?(r=Vh(e))?(e=r,fy):gy:e instanceof Vh?fy:e instanceof Date?yy:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?hy:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?vy:dy:py))(t,e)}function xy(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function Oy(t){return+t}var jy=[0,1];function Sy(t){return t}function Py(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function ky(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=Py(o,n),i=r(a,i)):(n=Py(n,o),i=r(i,a)),function(t){return i(n(t))}}function Ay(t,e,r){var n=Math.min(t.length,e.length)-1,o=new Array(n),i=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=Py(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=oh(t,e,1,n)-1;return i[r](o[r](e))}}function Ey(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function My(){var t,e,r,n,o,i,a=jy,c=jy,u=wy,l=Sy;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==Sy&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?Ay:ky,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),dy)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,Oy),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=xy,s()},f.clamp=function(t){return arguments.length?(l=!!t||Sy,s()):l!==Sy},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function _y(){return My()(Sy,Sy)}function Ty(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function Cy(t){return(t=Ty(Math.abs(t)))?t[1]:NaN}var Dy,Iy=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Ny(t){if(!(e=Iy.exec(t)))throw new Error("invalid format: "+t);var e;return new By({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function By(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Ly(t,e){var r=Ty(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+new Array(o-n.length+2).join("0")}Ny.prototype=By.prototype,By.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Ry={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>Ly(100*t,e),r:Ly,s:function(t,e){var r=Ty(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(Dy=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+new Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+Ty(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function zy(t){return t}var Uy,$y,qy,Fy=Array.prototype.map,Wy=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Hy(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?zy:(e=Fy.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?zy:function(t){return function(e){return e.replace(/[0-9]/g,function(e){return t[+e]})}}(Fy.call(t.numerals,String)),u=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Ny(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,y=t.width,d=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(d=!0,b="g"):Ry[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?i:/[%p]/.test(b)?u:"",x=Ry[b],O=/[defgprs%]/.test(b);function j(t){var o,i,u,p=g,j=w;if("c"===b)j=x(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:x(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0===+t&&"+"!==f&&(S=!1),p=(S?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===b?Wy[8+Dy/3]:"")+j+(S&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(u=t.charCodeAt(o))||u>57){j=(46===u?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}d&&!h&&(t=n(t,1/0));var P=p.length+t.length+j.length,k=P<y?new Array(y-P+1).join(e):"";switch(d&&h&&(t=n(k+t,k.length?y-j.length:1/0),k=""),r){case"<":t=p+t+j+k;break;case"=":t=p+k+t+j;break;case"^":t=k.slice(0,P=k.length>>1)+p+t+j+k.slice(P);break;default:t=k+p+t+j}return c(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var r=f(((t=Ny(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(Cy(e)/3))),o=Math.pow(10,-n),i=Wy[8+n/3];return function(t){return r(o*t)+i}}}}function Vy(t,e,r,n){var o,i=dh(t,e,r);switch((n=Ny(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Cy(e)/3)))-Cy(Math.abs(t)))}(i,a))||(n.precision=o),qy(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Cy(e)-Cy(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=function(t){return Math.max(0,-Cy(Math.abs(t)))}(i))||(n.precision=o-2*("%"===n.type))}return $y(n)}function Xy(t){var e=t.domain;return t.ticks=function(t){var r=e();return hh(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return Vy(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=yh(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o}n=o}return t},t}function Gy(){var t=_y();return t.copy=function(){return Ey(t,Gy())},xh.apply(t,arguments),Xy(t)}function Yy(t,e){var r,n=0,o=(t=t.slice()).length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function Ky(t){return Math.log(t)}function Zy(t){return Math.exp(t)}function Jy(t){return-Math.log(-t)}function Qy(t){return-Math.exp(-t)}function td(t){return isFinite(t)?+("1e"+t):t<0?0:t}function ed(t){return(e,r)=>-t(-e,r)}function rd(t){const e=t(Ky,Zy),r=e.domain;let n,o,i=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?td:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),r()[0]<0?(n=ed(n),o=ed(o),t(Jy,Qy)):t(Ky,Zy),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],c=e[e.length-1];const u=c<a;u&&([a,c]=[c,a]);let l,s,f=n(a),p=n(c);const h=null==t?10:+t;let y=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),a>0){for(;f<=p;++f)for(l=1;l<i;++l)if(s=f<0?l/o(-f):l*o(f),!(s<a)){if(s>c)break;y.push(s)}}else for(;f<=p;++f)for(l=i-1;l>=1;--l)if(s=f>0?l/o(-f):l*o(f),!(s<a)){if(s>c)break;y.push(s)}2*y.length<h&&(y=hh(a,c,h))}else y=hh(f,p,Math.min(p-f,h)).map(o);return u?y.reverse():y},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===i?"s":","),"function"!=typeof r&&(i%1||null!=(r=Ny(r)).precision||(r.trim=!0),r=$y(r)),t===1/0)return r;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(n(t)));return e*i<i-.5&&(e*=i),e<=a?r(t):""}},e.nice=()=>r(Yy(r(),{floor:t=>o(Math.floor(n(t))),ceil:t=>o(Math.ceil(n(t)))})),e}function nd(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function od(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function id(t){var e=1,r=t(nd(e),od(e));return r.constant=function(r){return arguments.length?t(nd(e=+r),od(e)):e},Xy(r)}function ad(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function cd(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function ud(t){return t<0?-t*t:t*t}function ld(t){var e=t(Sy,Sy),r=1;return e.exponent=function(e){return arguments.length?1===(r=+e)?t(Sy,Sy):.5===r?t(cd,ud):t(ad(r),ad(1/r)):r},Xy(e)}function sd(){var t=ld(My());return t.copy=function(){return Ey(t,sd()).exponent(t.exponent())},xh.apply(t,arguments),t}function fd(t){return Math.sign(t)*t*t}Uy=Hy({thousands:",",grouping:[3],currency:["$",""]}),$y=Uy.format,qy=Uy.formatPrefix;const pd=new Date,hd=new Date;function yd(t,e,r,n){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{const e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{const a=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n&&i>0))return a;let c;do{a.push(c=new Date(+r)),e(r,i),t(r)}while(c<r&&r<n);return a},o.filter=r=>yd(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(pd.setTime(+e),hd.setTime(+n),t(pd),t(hd),Math.floor(r(pd,hd))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(n?e=>n(e)%t===0:e=>o.count(0,e)%t===0):o:null)),o}const dd=yd(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);dd.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?yd(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):dd:null),dd.range;const vd=1e3,md=6e4,bd=36e5,gd=864e5,wd=6048e5,xd=2592e6,Od=31536e6,jd=yd(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*vd)},(t,e)=>(e-t)/vd,t=>t.getUTCSeconds());jd.range;const Sd=yd(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*vd)},(t,e)=>{t.setTime(+t+e*md)},(t,e)=>(e-t)/md,t=>t.getMinutes());Sd.range;const Pd=yd(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*md)},(t,e)=>(e-t)/md,t=>t.getUTCMinutes());Pd.range;const kd=yd(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*vd-t.getMinutes()*md)},(t,e)=>{t.setTime(+t+e*bd)},(t,e)=>(e-t)/bd,t=>t.getHours());kd.range;const Ad=yd(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*bd)},(t,e)=>(e-t)/bd,t=>t.getUTCHours());Ad.range;const Ed=yd(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*md)/gd,t=>t.getDate()-1);Ed.range;const Md=yd(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/gd,t=>t.getUTCDate()-1);Md.range;const _d=yd(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/gd,t=>Math.floor(t/gd));function Td(t){return yd(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*md)/wd)}_d.range;const Cd=Td(0),Dd=Td(1),Id=Td(2),Nd=Td(3),Bd=Td(4),Ld=Td(5),Rd=Td(6);function zd(t){return yd(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/wd)}Cd.range,Dd.range,Id.range,Nd.range,Bd.range,Ld.range,Rd.range;const Ud=zd(0),$d=zd(1),qd=zd(2),Fd=zd(3),Wd=zd(4),Hd=zd(5),Vd=zd(6);Ud.range,$d.range,qd.range,Fd.range,Wd.range,Hd.range,Vd.range;const Xd=yd(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear()),t=>t.getMonth());Xd.range;const Gd=yd(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth());Gd.range;const Yd=yd(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());Yd.every=t=>isFinite(t=Math.floor(t))&&t>0?yd(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,Yd.range;const Kd=yd(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function Zd(t,e,r,n,o,i){const a=[[jd,1,vd],[jd,5,5e3],[jd,15,15e3],[jd,30,3e4],[i,1,md],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,bd],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,gd],[n,2,1728e5],[r,1,wd],[e,1,xd],[e,3,7776e6],[t,1,Od]];function c(e,r,n){const o=Math.abs(r-e)/n,i=eh(([,,t])=>t).right(a,o);if(i===a.length)return t.every(dh(e/Od,r/Od,n));if(0===i)return dd.every(Math.max(dh(e,r,n),1));const[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}Kd.every=t=>isFinite(t=Math.floor(t))&&t>0?yd(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,Kd.range;const[Jd,Qd]=Zd(Kd,Gd,Ud,_d,Ad,Pd),[tv,ev]=Zd(Yd,Xd,Cd,Ed,kd,Sd);function rv(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function nv(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ov(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var iv,av,cv,uv={"-":"",_:" ",0:"0"},lv=/^\s*\d+/,sv=/^%/,fv=/[\\^$*+?|[\]().{}]/g;function pv(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?new Array(r-i+1).join(e)+o:o)}function hv(t){return t.replace(fv,"\\$&")}function yv(t){return new RegExp("^(?:"+t.map(hv).join("|")+")","i")}function dv(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function vv(t,e,r){var n=lv.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function mv(t,e,r){var n=lv.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function bv(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function gv(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function wv(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function xv(t,e,r){var n=lv.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function Ov(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function jv(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Sv(t,e,r){var n=lv.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function Pv(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function kv(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function Av(t,e,r){var n=lv.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function Ev(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function Mv(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function _v(t,e,r){var n=lv.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function Tv(t,e,r){var n=lv.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function Cv(t,e,r){var n=lv.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Dv(t,e,r){var n=sv.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function Iv(t,e,r){var n=lv.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function Nv(t,e,r){var n=lv.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function Bv(t,e){return pv(t.getDate(),e,2)}function Lv(t,e){return pv(t.getHours(),e,2)}function Rv(t,e){return pv(t.getHours()%12||12,e,2)}function zv(t,e){return pv(1+Ed.count(Yd(t),t),e,3)}function Uv(t,e){return pv(t.getMilliseconds(),e,3)}function $v(t,e){return Uv(t,e)+"000"}function qv(t,e){return pv(t.getMonth()+1,e,2)}function Fv(t,e){return pv(t.getMinutes(),e,2)}function Wv(t,e){return pv(t.getSeconds(),e,2)}function Hv(t){var e=t.getDay();return 0===e?7:e}function Vv(t,e){return pv(Cd.count(Yd(t)-1,t),e,2)}function Xv(t){var e=t.getDay();return e>=4||0===e?Bd(t):Bd.ceil(t)}function Gv(t,e){return t=Xv(t),pv(Bd.count(Yd(t),t)+(4===Yd(t).getDay()),e,2)}function Yv(t){return t.getDay()}function Kv(t,e){return pv(Dd.count(Yd(t)-1,t),e,2)}function Zv(t,e){return pv(t.getFullYear()%100,e,2)}function Jv(t,e){return pv((t=Xv(t)).getFullYear()%100,e,2)}function Qv(t,e){return pv(t.getFullYear()%1e4,e,4)}function tm(t,e){var r=t.getDay();return pv((t=r>=4||0===r?Bd(t):Bd.ceil(t)).getFullYear()%1e4,e,4)}function em(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+pv(e/60|0,"0",2)+pv(e%60,"0",2)}function rm(t,e){return pv(t.getUTCDate(),e,2)}function nm(t,e){return pv(t.getUTCHours(),e,2)}function om(t,e){return pv(t.getUTCHours()%12||12,e,2)}function im(t,e){return pv(1+Md.count(Kd(t),t),e,3)}function am(t,e){return pv(t.getUTCMilliseconds(),e,3)}function cm(t,e){return am(t,e)+"000"}function um(t,e){return pv(t.getUTCMonth()+1,e,2)}function lm(t,e){return pv(t.getUTCMinutes(),e,2)}function sm(t,e){return pv(t.getUTCSeconds(),e,2)}function fm(t){var e=t.getUTCDay();return 0===e?7:e}function pm(t,e){return pv(Ud.count(Kd(t)-1,t),e,2)}function hm(t){var e=t.getUTCDay();return e>=4||0===e?Wd(t):Wd.ceil(t)}function ym(t,e){return t=hm(t),pv(Wd.count(Kd(t),t)+(4===Kd(t).getUTCDay()),e,2)}function dm(t){return t.getUTCDay()}function vm(t,e){return pv($d.count(Kd(t)-1,t),e,2)}function mm(t,e){return pv(t.getUTCFullYear()%100,e,2)}function bm(t,e){return pv((t=hm(t)).getUTCFullYear()%100,e,2)}function gm(t,e){return pv(t.getUTCFullYear()%1e4,e,4)}function wm(t,e){var r=t.getUTCDay();return pv((t=r>=4||0===r?Wd(t):Wd.ceil(t)).getUTCFullYear()%1e4,e,4)}function xm(){return"+0000"}function Om(){return"%"}function jm(t){return+t}function Sm(t){return Math.floor(+t/1e3)}function Pm(t){return new Date(t)}function km(t){return t instanceof Date?+t:+new Date(+t)}function Am(t,e,r,n,o,i,a,c,u,l){var s=_y(),f=s.invert,p=s.domain,h=l(".%L"),y=l(":%S"),d=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),w=l("%Y");function x(t){return(u(t)<t?h:c(t)<t?y:a(t)<t?d:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:w)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,km)):p().map(Pm)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?x:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(Yy(r,t)):s},s.copy=function(){return Ey(s,Am(t,e,r,n,o,i,a,c,u,l))},s}function Em(){var t,e,r,n,o,i=0,a=1,c=Sy,u=!1;function l(e){return null==e||isNaN(e=+e)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(wy),l.rangeRound=s(xy),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function Mm(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function _m(){var t=ld(Em());return t.copy=function(){return Mm(t,_m()).exponent(t.exponent())},Oh.apply(t,arguments)}function Tm(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=Sy,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function y(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=wy);for(var r=0,n=e.length-1,o=e[0],i=new Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c=+c),e=i(u=+u),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=y(wy),h.rangeRound=y(xy),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function Cm(){var t=ld(Tm());return t.copy=function(){return Mm(t,Cm()).exponent(t.exponent())},Oh.apply(t,arguments)}!function(t){iv=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=yv(o),s=dv(o),f=yv(i),p=dv(i),h=yv(a),y=dv(a),d=yv(c),v=dv(c),m=yv(u),b=dv(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:Bv,e:Bv,f:$v,g:Jv,G:tm,H:Lv,I:Rv,j:zv,L:Uv,m:qv,M:Fv,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:jm,s:Sm,S:Wv,u:Hv,U:Vv,V:Gv,w:Yv,W:Kv,x:null,X:null,y:Zv,Y:Qv,Z:em,"%":Om},w={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:rm,e:rm,f:cm,g:bm,G:wm,H:nm,I:om,j:im,L:am,m:um,M:lm,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:jm,s:Sm,S:sm,u:fm,U:pm,V:ym,w:dm,W:vm,x:null,X:null,y:mm,Y:gm,Z:xm,"%":Om},x={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=y.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:kv,e:kv,f:Cv,g:Ov,G:xv,H:Ev,I:Ev,j:Av,L:Tv,m:Pv,M:Mv,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:Sv,Q:Iv,s:Nv,S:_v,u:mv,U:bv,V:gv,w:vv,W:wv,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:Ov,Y:xv,Z:jv,"%":Dv};function O(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=uv[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=ov(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(n=nv(ov(i.y,0,1))).getUTCDay(),n=o>4||0===o?$d.ceil(n):$d(n),n=Md.offset(n,7*(i.V-1)),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(o=(n=rv(ov(i.y,0,1))).getDay(),n=o>4||0===o?Dd.ceil(n):Dd(n),n=Ed.offset(n,7*(i.V-1)),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?nv(ov(i.y,0,1)).getUTCDay():rv(ov(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,nv(i)):rv(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=x[o in uv?e.charAt(a++):o])||(n=i(t,r,n))<0)return-1}else if(o!=r.charCodeAt(n++))return-1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),w.x=O(r,w),w.X=O(n,w),w.c=O(e,w),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",w);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),av=iv.format,iv.parse,cv=iv.utcFormat,iv.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});const Dm=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Ph,scaleDiverging:function t(){var e=Xy(Tm()(Sy));return e.copy=function(){return Mm(e,t())},Oh.apply(e,arguments)},scaleDivergingLog:function t(){var e=rd(Tm()).domain([.1,1,10]);return e.copy=function(){return Mm(e,t()).base(e.base())},Oh.apply(e,arguments)},scaleDivergingPow:Cm,scaleDivergingSqrt:function(){return Cm.apply(null,arguments).exponent(.5)},scaleDivergingSymlog:function t(){var e=id(Tm());return e.copy=function(){return Mm(e,t()).constant(e.constant())},Oh.apply(e,arguments)},scaleIdentity:function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,Oy),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,Oy):[0,1],Xy(n)},scaleImplicit:jh,scaleLinear:Gy,scaleLog:function t(){const e=rd(My()).domain([1,10]);return e.copy=()=>Ey(e,t()).base(e.base()),xh.apply(e,arguments),e},scaleOrdinal:Sh,scalePoint:Ah,scalePow:sd,scaleQuantile:function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=new Array(e-1);++t<e;)o[t-1]=wh(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[oh(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();r=[];for(let e of t)null==e||isNaN(e=+e)||r.push(e);return r.sort(Qp),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},xh.apply(a,arguments)},scaleQuantize:function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[oh(i,t,0,o)]:e}function u(){var t=-1;for(i=new Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length?(e=t,c):c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},xh.apply(Xy(c),arguments)},scaleRadial:function t(){var e,r=_y(),n=[0,1],o=!1;function i(t){var n=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(r(t));return isNaN(n)?e:o?Math.round(n):n}return i.invert=function(t){return r.invert(fd(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,Oy)).map(fd)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},xh.apply(i,arguments),Xy(i)},scaleSequential:function t(){var e=Xy(Em()(Sy));return e.copy=function(){return Mm(e,t())},Oh.apply(e,arguments)},scaleSequentialLog:function t(){var e=rd(Em()).domain([1,10]);return e.copy=function(){return Mm(e,t()).base(e.base())},Oh.apply(e,arguments)},scaleSequentialPow:_m,scaleSequentialQuantile:function t(){var e=[],r=Sy;function n(t){if(null!=t&&!isNaN(t=+t))return r((oh(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(Qp),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>function(t,e){if((r=(t=Float64Array.from(function*(t){for(let e of t)null!=e&&(e=+e)>=e&&(yield e)}(t))).length)&&!isNaN(e=+e)){if(e<=0||r<2)return mh(t);if(e>=1)return vh(t);var r,n=(r-1)*e,o=Math.floor(n),i=vh(bh(t,o).subarray(0,o+1));return i+(mh(t.subarray(o+1))-i)*(n-o)}}(e,n/t))},n.copy=function(){return t(r).domain(e)},Oh.apply(n,arguments)},scaleSequentialSqrt:function(){return _m.apply(null,arguments).exponent(.5)},scaleSequentialSymlog:function t(){var e=id(Em());return e.copy=function(){return Mm(e,t()).constant(e.constant())},Oh.apply(e,arguments)},scaleSqrt:function(){return sd.apply(null,arguments).exponent(.5)},scaleSymlog:function t(){var e=id(My());return e.copy=function(){return Ey(e,t()).constant(e.constant())},xh.apply(e,arguments)},scaleThreshold:function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[oh(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(r=Array.from(t),o=Math.min(r.length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},xh.apply(i,arguments)},scaleTime:function(){return xh.apply(Am(tv,ev,Yd,Xd,Cd,Ed,kd,Sd,jd,av).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},scaleUtc:function(){return xh.apply(Am(Jd,Qd,Kd,Gd,Ud,Md,Ad,Pd,jd,cv).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},tickFormat:Vy},Symbol.toStringTag,{value:"Module"}));var Im=Wt;var Nm=function(t,e,r){for(var n=-1,o=t.length;++n<o;){var i=t[n],a=e(i);if(null!=a&&(void 0===c?a==a&&!Im(a):r(a,c)))var c=a,u=i}return u};var Bm=Nm,Lm=function(t,e){return t>e},Rm=dl;const zm=r(function(t){return t&&t.length?Bm(t,Rm,Lm):void 0});var Um=Nm,$m=function(t,e){return t<e},qm=dl;const Fm=r(function(t){return t&&t.length?Um(t,qm,$m):void 0});var Wm=Pr,Hm=kl,Vm=ws,Xm=St;var Gm=ps,Ym=function(t,e){return(Xm(t)?Wm:Vm)(t,Hm(e))};const Km=r(function(t,e){return Gm(Ym(t,e),1)});var Zm=Fu;const Jm=r(function(t,e){return Zm(t,e)});var Qm,tb=1e9,eb=!0,rb="[DecimalError] ",nb=rb+"Invalid argument: ",ob=rb+"Exponent out of range: ",ib=Math.floor,ab=Math.pow,cb=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ub=1e7,lb=9007199254740991,sb=ib(1286742750677284.5),fb={};function pb(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),eb?Ob(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/ub|0,u[i]%=ub;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,eb?Ob(e,f):e}function hb(t,e,r){if(t!==~~t||t<e||t>r)throw Error(nb+t)}function yb(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=gb(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=gb(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}fb.absoluteValue=fb.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},fb.comparedTo=fb.cmp=function(t){var e,r,n,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,r=(n=i.d.length)<(o=t.d.length)?n:o;e<r;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return n===o?0:n>o^i.s<0?1:-1},fb.decimalPlaces=fb.dp=function(){var t=this,e=t.d.length-1,r=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},fb.dividedBy=fb.div=function(t){return db(this,new this.constructor(t))},fb.dividedToIntegerBy=fb.idiv=function(t){var e=this.constructor;return Ob(db(this,new e(t),0,1),e.precision)},fb.equals=fb.eq=function(t){return!this.cmp(t)},fb.exponent=function(){return mb(this)},fb.greaterThan=fb.gt=function(t){return this.cmp(t)>0},fb.greaterThanOrEqualTo=fb.gte=function(t){return this.cmp(t)>=0},fb.isInteger=fb.isint=function(){return this.e>this.d.length-2},fb.isNegative=fb.isneg=function(){return this.s<0},fb.isPositive=fb.ispos=function(){return this.s>0},fb.isZero=function(){return 0===this.s},fb.lessThan=fb.lt=function(t){return this.cmp(t)<0},fb.lessThanOrEqualTo=fb.lte=function(t){return this.cmp(t)<1},fb.logarithm=fb.log=function(t){var e,r=this,n=r.constructor,o=n.precision,i=o+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(Qm))throw Error(rb+"NaN");if(r.s<1)throw Error(rb+(r.s?"NaN":"-Infinity"));return r.eq(Qm)?new n(0):(eb=!1,e=db(wb(r,i),wb(t,i),i),eb=!0,Ob(e,o))},fb.minus=fb.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?jb(e,t):pb(e,(t.s=-t.s,t))},fb.modulo=fb.mod=function(t){var e,r=this,n=r.constructor,o=n.precision;if(!(t=new n(t)).s)throw Error(rb+"NaN");return r.s?(eb=!1,e=db(r,t,0,1).times(t),eb=!0,r.minus(e)):Ob(new n(r),o)},fb.naturalExponential=fb.exp=function(){return vb(this)},fb.naturalLogarithm=fb.ln=function(){return wb(this)},fb.negated=fb.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},fb.plus=fb.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?pb(e,t):jb(e,(t.s=-t.s,t))},fb.precision=fb.sd=function(t){var e,r,n,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(nb+t);if(e=mb(o)+1,r=7*(n=o.d.length-1)+1,n=o.d[n]){for(;n%10==0;n/=10)r--;for(n=o.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},fb.squareRoot=fb.sqrt=function(){var t,e,r,n,o,i,a,c=this,u=c.constructor;if(c.s<1){if(!c.s)return new u(0);throw Error(rb+"NaN")}for(t=mb(c),eb=!1,0==(o=Math.sqrt(+c))||o==1/0?(((e=yb(c.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=ib((t+1)/2)-(t<0||t%2),n=new u(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new u(o.toString()),o=a=(r=u.precision)+3;;)if(n=(i=n).plus(db(c,i,a+2)).times(.5),yb(i.d).slice(0,a)===(e=yb(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(Ob(i,r+1,0),i.times(i).eq(c)){n=i;break}}else if("9999"!=e)break;a+=4}return eb=!0,Ob(n,r)},fb.times=fb.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this,f=s.constructor,p=s.d,h=(t=new f(t)).d;if(!s.s||!t.s)return new f(0);for(t.s*=s.s,r=s.e+t.e,(u=p.length)<(l=h.length)&&(i=p,p=h,h=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+h[n]*p[o-n-1]+e,i[o--]=c%ub|0,e=c/ub|0;i[o]=(i[o]+e)%ub|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,eb?Ob(t,f.precision):t},fb.toDecimalPlaces=fb.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(hb(t,0,tb),void 0===e?e=n.rounding:hb(e,0,8),Ob(r,t+mb(r)+1,e))},fb.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=Sb(n,!0):(hb(t,0,tb),void 0===e?e=o.rounding:hb(e,0,8),r=Sb(n=Ob(new o(n),t+1,e),!0,t+1)),r},fb.toFixed=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?Sb(o):(hb(t,0,tb),void 0===e?e=i.rounding:hb(e,0,8),r=Sb((n=Ob(new i(o),t+mb(o)+1,e)).abs(),!1,t+mb(n)+1),o.isneg()&&!o.isZero()?"-"+r:r)},fb.toInteger=fb.toint=function(){var t=this,e=t.constructor;return Ob(new e(t),mb(t)+1,e.rounding)},fb.toNumber=function(){return+this},fb.toPower=fb.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(Qm);if(!(c=new u(c)).s){if(t.s<1)throw Error(rb+"Infinity");return c}if(c.eq(Qm))return c;if(n=u.precision,t.eq(Qm))return Ob(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=lb){for(o=new u(Qm),e=Math.ceil(n/7+4),eb=!1;r%2&&Pb((o=o.times(c)).d,e),0!==(r=ib(r/2));)Pb((c=c.times(c)).d,e);return eb=!0,t.s<0?new u(Qm).div(o):Ob(o,n)}}else if(i<0)throw Error(rb+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,eb=!1,o=t.times(wb(c,n+12)),eb=!0,(o=vb(o)).s=i,o},fb.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?n=Sb(o,(r=mb(o))<=i.toExpNeg||r>=i.toExpPos):(hb(t,1,tb),void 0===e?e=i.rounding:hb(e,0,8),n=Sb(o=Ob(new i(o),t,e),t<=(r=mb(o))||r<=i.toExpNeg,t)),n},fb.toSignificantDigits=fb.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(hb(t,1,tb),void 0===e?e=r.rounding:hb(e,0,8)),Ob(new r(this),t,e)},fb.toString=fb.valueOf=fb.val=fb.toJSON=fb[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=this,e=mb(t),r=t.constructor;return Sb(t,e<=r.toExpNeg||e>=r.toExpPos)};var db=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%ub|0,n=r/ub|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*ub+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,y,d,v,m,b,g,w,x,O,j,S,P=n.constructor,k=n.s==o.s?1:-1,A=n.d,E=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(rb+"Division by zero");for(u=n.e-o.e,j=E.length,x=A.length,y=(h=new P(k)).d=[],l=0;E[l]==(A[l]||0);)++l;if(E[l]>(A[l]||0)&&--u,(b=null==i?i=P.precision:a?i+(mb(n)-mb(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,E=E[0],b++;(l<x||s)&&b--;l++)g=s*ub+(A[l]||0),y[l]=g/E|0,s=g%E|0;else{for((s=ub/(E[0]+1)|0)>1&&(E=t(E,s),A=t(A,s),j=E.length,x=A.length),w=j,v=(d=A.slice(0,j)).length;v<j;)d[v++]=0;(S=E.slice()).unshift(0),O=E[0],E[1]>=ub/2&&++O;do{s=0,(c=e(E,d,j,v))<0?(m=d[0],j!=v&&(m=m*ub+(d[1]||0)),(s=m/O|0)>1?(s>=ub&&(s=ub-1),1==(c=e(f=t(E,s),d,p=f.length,v=d.length))&&(s--,r(f,j<p?S:E,p))):(0==s&&(c=s=1),f=E.slice()),(p=f.length)<v&&f.unshift(0),r(d,f,v),-1==c&&(c=e(E,d,j,v=d.length))<1&&(s++,r(d,j<v?S:E,v)),v=d.length):0===c&&(s++,d=[0]),y[l++]=s,c&&d[0]?d[v++]=A[w]||0:(d=[A[w]],v=1)}while((w++<x||void 0!==d[0])&&b--)}return y[0]||y.shift(),h.e=u,Ob(h,a?i+mb(h)+1:i)}}();function vb(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(mb(t)>16)throw Error(ob+mb(t));if(!t.s)return new l(Qm);for(null==e?(eb=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(ab(2,u))/Math.LN10*2+5|0,r=n=o=new l(Qm),l.precision=a;;){if(n=Ob(n.times(t),a),r=r.times(++c),yb((i=o.plus(db(n,r,a))).d).slice(0,a)===yb(o.d).slice(0,a)){for(;u--;)o=Ob(o.times(o),a);return l.precision=s,null==e?(eb=!0,Ob(o,s)):o}o=i}}function mb(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function bb(t,e,r){if(e>t.LN10.sd())throw eb=!0,r&&(t.precision=r),Error(rb+"LN10 precision limit exceeded");return Ob(new t(t.LN10),e)}function gb(t){for(var e="";t--;)e+="0";return e}function wb(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,y=p.constructor,d=y.precision;if(p.s<1)throw Error(rb+(p.s?"NaN":"-Infinity"));if(p.eq(Qm))return new y(0);if(null==e?(eb=!1,l=d):l=e,p.eq(10))return null==e&&(eb=!0),bb(y,l);if(l+=10,y.precision=l,n=(r=yb(h)).charAt(0),i=mb(p),!(Math.abs(i)<15e14))return u=bb(y,l+2,d).times(i+""),p=wb(new y(n+"."+r.slice(1)),l-10).plus(u),y.precision=d,null==e?(eb=!0,Ob(p,d)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=yb((p=p.times(t)).d)).charAt(0),f++;for(i=mb(p),n>1?(p=new y("0."+r),i++):p=new y(n+"."+r.slice(1)),c=a=p=db(p.minus(Qm),p.plus(Qm),l),s=Ob(p.times(p),l),o=3;;){if(a=Ob(a.times(s),l),yb((u=c.plus(db(a,new y(o),l))).d).slice(0,l)===yb(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(bb(y,l+2,d).times(i+""))),c=db(c,new y(f),l),y.precision=d,null==e?(eb=!0,Ob(c,d)):c;c=u,o+=2}}function xb(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=ib(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),eb&&(t.e>sb||t.e<-sb))throw Error(ob+r)}else t.s=0,t.e=0,t.d=[0];return t}function Ob(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(l=i=f[s],a=1;i>=10;i/=10)a++;o=(n%=7)-7+a}if(void 0!==r&&(c=l/(i=ab(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/ab(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=mb(t),f.length=1,e=e-i-1,f[0]=ab(10,(7-e%7)%7),t.e=ib(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=ab(10,7-n),f[s]=o>0?(l/ab(10,a-o)%ab(10,o)|0)*i:0),u)for(;;){if(0==s){(f[0]+=i)==ub&&(f[0]=1,++t.e);break}if(f[s]+=i,f[s]!=ub)break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(eb&&(t.e>sb||t.e<-sb))throw Error(ob+mb(t));return t}function jb(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),eb?Ob(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=ub-1;--u[i],u[o]+=ub}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,eb?Ob(e,h):e):new p(0)}function Sb(t,e,r){var n,o=mb(t),i=yb(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+gb(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+gb(-o-1)+i,r&&(n=r-a)>0&&(i+=gb(n))):o>=a?(i+=gb(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+gb(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=gb(n))),t.s<0?"-"+i:i}function Pb(t,e){if(t.length>e)return t.length=e,!0}function kb(t){if(!t||"object"!=typeof t)throw Error(rb+"Object expected");var e,r,n,o=["precision",1,tb,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(!(ib(n)===n&&n>=o[e+1]&&n<=o[e+2]))throw Error(nb+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(nb+r+": "+n);this[r]=new this(n)}return this}var Ab=function t(e){var r,n,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"==typeof t){if(0*t!=0)throw Error(nb+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):xb(e,t.toString())}if("string"!=typeof t)throw Error(nb+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!cb.test(t))throw Error(nb+t);xb(e,t)}if(i.prototype=fb,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=kb,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});Qm=new Ab(1);const Eb=Ab;function Mb(t){return function(t){if(Array.isArray(t))return _b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return _b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _b(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Tb=function(t){return t},Cb={"@@functional/placeholder":!0},Db=function(t){return t===Cb},Ib=function(t){return function e(){return 0===arguments.length||1===arguments.length&&Db(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Nb=function t(e,r){return 1===e?r:Ib(function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==Cb}).length;return a>=e?r.apply(void 0,o):t(e-a,Ib(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return Db(t)?e.shift():t});return r.apply(void 0,Mb(i).concat(e))}))})},Bb=function(t){return Nb(t.length,t)},Lb=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},Rb=Bb(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),zb=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Ub=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};const $b={rangeStep:function(t,e,r){for(var n=new Eb(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new Eb(t).abs().log(10).toNumber())+1},interpolateNumber:Bb(function(t,e,r){var n=+t;return n+r*(+e-n)}),uninterpolateNumber:Bb(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uninterpolateTruncation:Bb(function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))})};function qb(t){return function(t){if(Array.isArray(t))return Hb(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Wb(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fb(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(u){o=!0,i=u}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}(t,e)||Wb(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wb(t,e){if(t){if("string"==typeof t)return Hb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Hb(t,e):void 0}}function Hb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Vb(t){var e=Fb(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function Xb(t,e,r){if(t.lte(0))return new Eb(0);var n=$b.getDigitCount(t.toNumber()),o=new Eb(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new Eb(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new Eb(Math.ceil(c))}function Gb(t,e,r){var n=1,o=new Eb(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new Eb(10).pow($b.getDigitCount(t)-1),o=new Eb(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new Eb(Math.floor(t)))}else 0===t?o=new Eb(Math.floor((e-1)/2)):r||(o=new Eb(Math.floor(t)));var a=Math.floor((e-1)/2),c=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Tb;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}}(Rb(function(t){return o.add(new Eb(t-a).mul(n)).toNumber()}),Lb);return c(0,e)}function Yb(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new Eb(0),tickMin:new Eb(0),tickMax:new Eb(0)};var i,a=Xb(new Eb(e).sub(t).div(r-1),n,o);i=t<=0&&e>=0?new Eb(0):(i=new Eb(t).add(e).div(2)).sub(new Eb(i).mod(a));var c=Math.ceil(i.sub(t).div(a).toNumber()),u=Math.ceil(new Eb(e).sub(i).div(a).toNumber()),l=c+u+1;return l>r?Yb(t,e,r,n,o+1):(l<r&&(u=e>0?u+(r-l):u,c=e>0?c:c+(r-l)),{step:a,tickMin:i.sub(new Eb(c).mul(a)),tickMax:i.add(new Eb(u).mul(a))})}var Kb=Ub(function(t){var e=Fb(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),c=Fb(Vb([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(qb(Lb(0,o-1).map(function(){return 1/0}))):[].concat(qb(Lb(0,o-1).map(function(){return-1/0})),[l]);return r>n?zb(s):s}if(u===l)return Gb(u,o,i);var f=Yb(u,l,a,i),p=f.step,h=f.tickMin,y=f.tickMax,d=$b.rangeStep(h,y.add(new Eb(.1).mul(p)),p);return r>n?zb(d):d}),Zb=Ub(function(t,e){var r=Fb(t,2),n=r[0],o=r[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Fb(Vb([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=Xb(new Eb(u).sub(c).div(l-1),i,0),f=[].concat(qb($b.rangeStep(new Eb(c),new Eb(u).sub(new Eb(.99).mul(s)),s)),[u]);return n>o?zb(f):f});function Jb(t,e){throw new Error("Invariant failed")}var Qb=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function tg(t){return(tg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eg(){return eg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},eg.apply(this,arguments)}function rg(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ng(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ng(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ng(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function og(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ig(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fg(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function ag(t,e,r){return e=ug(e),function(t,e){if(e&&("object"===tg(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,cg()?Reflect.construct(e,r||[],ug(t).constructor):e.apply(t,r))}function cg(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(cg=function(){return!!t})()}function ug(t){return(ug=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lg(t,e){return(lg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function sg(t,e,r){return(e=fg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fg(t){var e=function(t,e){if("object"!=tg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tg(e)?e:e+""}var pg=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),ag(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lg(t,e)}(t,n.Component),ig(t,[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,o=t.width,i=t.dataKey,a=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=og(t,Qb),f=ro(s,!1);"x"===this.props.direction&&"number"!==u.type&&Jb();var p=a.map(function(t){var a=c(t,i),s=a.x,p=a.y,h=a.value,y=a.errorVal;if(!y)return null;var d,v,m=[];if(Array.isArray(y)){var b=rg(y,2);d=b[0],v=b[1]}else d=v=y;if("vertical"===r){var g=u.scale,w=p+e,x=w+o,O=w-o,j=g(h-d),S=g(h+v);m.push({x1:S,y1:x,x2:S,y2:O}),m.push({x1:j,y1:w,x2:S,y2:w}),m.push({x1:j,y1:x,x2:j,y2:O})}else if("horizontal"===r){var P=l.scale,k=s+e,A=k-o,E=k+o,M=P(h-d),_=P(h+v);m.push({x1:A,y1:_,x2:E,y2:_}),m.push({x1:k,y1:M,x2:k,y2:_}),m.push({x1:A,y1:M,x2:E,y2:M})}return n.createElement(ho,eg({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},f),m.map(function(t){return n.createElement("line",eg({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(ho,{className:"recharts-errorBars"},p)}}])}();function hg(t){return(hg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yg(Object(r),!0).forEach(function(e){vg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function vg(t,e,r){var n;return n=function(t,e){if("object"!=hg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==hg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}sg(pg,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),sg(pg,"displayName","ErrorBar");var mg=function(t){var e=t.children,r=t.formattedGraphicalItems,n=t.legendWidth,o=t.legendContent,i=Qn(e,as);if(!i)return null;var a,c=as.defaultProps,u=void 0!==c?dg(dg({},c),i.props):{};return a=i.props&&i.props.payload?i.props&&i.props.payload:"children"===o?(r||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:i.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(r||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?dg(dg({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:kg(e),value:i||o,payload:n}}),dg(dg(dg({},u),as.getWithHeight(i,n)),{},{payload:a,item:i})};function bg(t){return(bg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function gg(t){return function(t){if(Array.isArray(t))return wg(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return wg(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wg(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wg(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function xg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Og(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xg(Object(r),!0).forEach(function(e){jg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function jg(t,e,r){var n;return n=function(t,e){if("object"!=bg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=bg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==bg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Sg(t,e,r){return Xr(t)||Xr(e)?r:An(e)?Vr(t,e,r):ee(e)?e(t):r}function Pg(t,e,r,n){var o=Km(t,function(t){return Sg(t,e)});if("number"===r){var i=o.filter(function(t){return kn(t)||parseFloat(t)});return i.length?[Fm(i),zm(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!Xr(t)}):o).map(function(t){return An(t)||t instanceof Date?t:""})}var kg=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?Og(Og({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},Ag=function(t,e,r,n,o){var i=Jn(e.props.children,pg).filter(function(t){return function(t,e,r){return!!Xr(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=Sg(e,r);if(Xr(n))return t;var o=Array.isArray(n)?[Fm(n),zm(n)]:[n,n],i=a.reduce(function(t,r){var n=Sg(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},Eg=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&Ag(t,e,i,n)||Pg(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},Mg=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},_g=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},Tg=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*Sn(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map(function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}}).filter(function(t){return!jn(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},Cg=new WeakMap,Dg=function(t,e){if("function"!=typeof e)return t;Cg.has(t)||Cg.set(t,new WeakMap);var r=Cg.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},Ig=1e-4,Ng={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=jn(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}$i(t,e)}},none:$i,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}$i(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var y=t[e[h]];p+=(y[a][1]||0)-(y[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,$i(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=jn(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},Bg=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=Ng[r],i=function(){var t=$o([]),e=qi,r=$i,n=Fi;function o(o){var i,a,c=Array.from(t.apply(this,arguments),Wi),u=c.length,l=-1;for(const t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=ti(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:$o(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:$o(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?qi:"function"==typeof t?t:$o(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?$i:t,o):r},o}().keys(n).value(function(t,e){return+Sg(t,e,0)}).order(qi).offset(o);return i(t)};function Lg(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!Xr(o[e.dataKey])){var c=Dn(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=Sg(o,Xr(a)?e.dataKey:a);return Xr(u)?null:e.scale(u)}var Rg=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=Sg(i,e.dataKey,e.domain[a]);return Xr(c)?null:e.scale(c)-o/2+n},zg=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[Fm(e.concat([t[0]]).filter(kn)),zm(e.concat([t[1]]).filter(kn))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},Ug=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,$g=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,qg=function(t,e,r){if(ee(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(kn(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(Ug.test(t[0])){var o=+Ug.exec(t[0])[1];n[0]=e[0]-o}else ee(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(kn(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if($g.test(t[1])){var i=+$g.exec(t[1])[1];n[1]=e[1]+i}else ee(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},Fg=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=ef(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},Wg=function(t,e,r){return t&&t.length?Jm(t,Vr(r,"type.defaultProps.domain"))?e:t:e},Hg=function(t,e){var r=t.type.defaultProps?Og(Og({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return Og(Og({},ro(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:kg(t),value:Sg(e,n),type:c,payload:e,chartType:u,hide:l})};function Vg(t){return(Vg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Xg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Gg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Xg(Object(r),!0).forEach(function(e){Yg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Yg(t,e,r){var n;return n=function(t,e){if("object"!=Vg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Vg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Vg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Kg=Math.PI/180,Zg=function(t){return 180*t/Math.PI},Jg=function(t,e,r,n){return{x:t+Math.cos(-Kg*n)*r,y:e+Math.sin(-Kg*n)*r}},Qg=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=(r-o)/a,u=Math.acos(c);return n>i&&(u=2*Math.PI-u),{radius:a,angle:Zg(u),angleInRadian:u}},tw=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},ew=function(t,e){var r=t.x,n=t.y,o=Qg({x:r,y:n},e),i=o.radius,a=o.angle,c=e.innerRadius,u=e.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),f=s.startAngle,p=s.endAngle,h=a;if(f<=p){for(;h>p;)h-=360;for(;h<f;)h+=360;l=h>=f&&h<=p}else{for(;h>f;)h-=360;for(;h<p;)h+=360;l=h>=p&&h<=f}return l?Gg(Gg({},e),{},{radius:i,angle:tw(h,e)}):null};function rw(t){return(rw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var nw=["offset"];function ow(t){return function(t){if(Array.isArray(t))return iw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return iw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iw(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function aw(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function cw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cw(Object(r),!0).forEach(function(e){lw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lw(t,e,r){var n;return n=function(t,e){if("object"!=rw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==rw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sw(){return sw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},sw.apply(this,arguments)}var fw=function(t,e,r){var o,i,a=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c,f=s.cx,p=s.cy,h=s.innerRadius,y=s.outerRadius,d=s.startAngle,v=s.endAngle,m=s.clockWise,b=(h+y)/2,g=function(t,e){return Sn(e-t)*Math.min(Math.abs(e-t),360)}(d,v),w=g>=0?1:-1;"insideStart"===a?(o=d+w*u,i=m):"insideEnd"===a?(o=v-w*u,i=!m):"end"===a&&(o=v+w*u,i=m),i=g<=0?i:!i;var x=Jg(f,p,b,o),O=Jg(f,p,b,o+359*(i?1:-1)),j="M".concat(x.x,",").concat(x.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(i?0:1,",\n    ").concat(O.x,",").concat(O.y),S=Xr(t.id)?Mn("recharts-radial-line-"):t.id;return n.createElement("text",sw({},r,{dominantBaseline:"central",className:jt("recharts-radial-bar-label",l)}),n.createElement("defs",null,n.createElement("path",{id:S,d:j})),n.createElement("textPath",{xlinkHref:"#".concat(S)},e))};function pw(e){var r,o=e.offset,i=uw({offset:void 0===o?5:o},aw(e,nw)),a=i.viewBox,c=i.position,u=i.value,l=i.children,s=i.content,f=i.className,p=void 0===f?"":f,h=i.textBreakAll;if(!a||Xr(u)&&Xr(l)&&!t.isValidElement(s)&&!ee(s))return null;if(t.isValidElement(s))return t.cloneElement(s,i);if(ee(s)){if(r=t.createElement(s,i),t.isValidElement(r))return r}else r=function(t){var e=t.value,r=t.formatter,n=Xr(t.children)?e:t.children;return ee(r)?r(n):n}(i);var y=function(t){return"cx"in t&&kn(t.cx)}(a),d=ro(i,!0);if(y&&("insideStart"===c||"insideEnd"===c||"end"===c))return fw(i,r,d);var v=y?function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,c=o.innerRadius,u=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=Jg(i,a,u+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=Jg(i,a,(c+u)/2,l);return{x:p.x,y:p.y,textAnchor:"middle",verticalAnchor:"middle"}}(i):function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,c=i.y,u=i.width,l=i.height,s=l>=0?1:-1,f=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=u>=0?1:-1,d=y*n,v=y>0?"end":"start",m=y>0?"start":"end";if("top"===o)return uw(uw({},{x:a+u/2,y:c-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(c-r.y,0),width:u}:{});if("bottom"===o)return uw(uw({},{x:a+u/2,y:c+l+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(c+l),0),width:u}:{});if("left"===o){var b={x:a-d,y:c+l/2,textAnchor:v,verticalAnchor:"middle"};return uw(uw({},b),r?{width:Math.max(b.x-r.x,0),height:l}:{})}if("right"===o){var g={x:a+u+d,y:c+l/2,textAnchor:m,verticalAnchor:"middle"};return uw(uw({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:l}:{})}var w=r?{width:u,height:l}:{};return"insideLeft"===o?uw({x:a+d,y:c+l/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===o?uw({x:a+u-d,y:c+l/2,textAnchor:v,verticalAnchor:"middle"},w):"insideTop"===o?uw({x:a+u/2,y:c+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===o?uw({x:a+u/2,y:c+l-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?uw({x:a+d,y:c+f,textAnchor:m,verticalAnchor:h},w):"insideTopRight"===o?uw({x:a+u-d,y:c+f,textAnchor:v,verticalAnchor:h},w):"insideBottomLeft"===o?uw({x:a+d,y:c+l-f,textAnchor:m,verticalAnchor:p},w):"insideBottomRight"===o?uw({x:a+u-d,y:c+l-f,textAnchor:v,verticalAnchor:p},w):Zt(o)&&(kn(o.x)||Pn(o.x))&&(kn(o.y)||Pn(o.y))?uw({x:a+_n(o.x,u),y:c+_n(o.y,l),textAnchor:"end",verticalAnchor:"end"},w):uw({x:a+u/2,y:c+l/2,textAnchor:"middle",verticalAnchor:"middle"},w)}(i);return n.createElement(Jp,sw({className:jt("recharts-label",p)},d,v,{breakAll:h}),r)}pw.displayName="Label";var hw=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,d=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(kn(y)&&kn(d)){if(kn(s)&&kn(f))return{x:s,y:f,width:y,height:d};if(kn(p)&&kn(h))return{x:p,y:h,width:y,height:d}}return kn(s)&&kn(f)?{x:s,y:f,width:0,height:0}:kn(e)&&kn(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};pw.parseViewBox=hw,pw.renderCallByParent=function(e,r){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=e.children,a=hw(e),c=Jn(i,pw).map(function(e,n){return t.cloneElement(e,{viewBox:r||a,key:"label-".concat(n)})});if(!o)return c;var u=function(e,r){return e?!0===e?n.createElement(pw,{key:"label-implicit",viewBox:r}):An(e)?n.createElement(pw,{key:"label-implicit",viewBox:r,value:e}):t.isValidElement(e)?e.type===pw?t.cloneElement(e,{key:"label-implicit",viewBox:r}):n.createElement(pw,{key:"label-implicit",content:e,viewBox:r}):ee(e)?n.createElement(pw,{key:"label-implicit",content:e,viewBox:r}):Zt(e)?n.createElement(pw,sw({viewBox:r},e,{key:"label-implicit"})):null:null}(e.label,r||a);return[u].concat(ow(c))};const yw=r(function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0});function dw(t){return(dw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var vw=["valueAccessor"],mw=["data","dataKey","clockWise","id","textBreakAll"];function bw(t){return function(t){if(Array.isArray(t))return gw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return gw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gw(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ww(){return ww=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ww.apply(this,arguments)}function xw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ow(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xw(Object(r),!0).forEach(function(e){jw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function jw(t,e,r){var n;return n=function(t,e){if("object"!=dw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==dw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Sw(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Pw=function(t){return Array.isArray(t.value)?yw(t.value):t.value};function kw(t){var e=t.valueAccessor,r=void 0===e?Pw:e,o=Sw(t,vw),i=o.data,a=o.dataKey,c=o.clockWise,u=o.id,l=o.textBreakAll,s=Sw(o,mw);return i&&i.length?n.createElement(ho,{className:"recharts-label-list"},i.map(function(t,e){var o=Xr(a)?r(t,e):Sg(t&&t.payload,a),i=Xr(u)?{}:{id:"".concat(u,"-").concat(e)};return n.createElement(pw,ww({},ro(t,!0),s,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:l,viewBox:pw.parseViewBox(Xr(c)?t:Ow(Ow({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}function Aw(t){return(Aw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ew(){return Ew=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ew.apply(this,arguments)}function Mw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Mw(Object(r),!0).forEach(function(e){Tw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Tw(t,e,r){var n;return n=function(t,e){if("object"!=Aw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Aw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Aw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}kw.displayName="LabelList",kw.renderCallByParent=function(e,r){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=Jn(e.children,kw).map(function(e,n){return t.cloneElement(e,{data:r,key:"labelList-".concat(n)})});return o?[function(t,e){return t?!0===t?n.createElement(kw,{key:"labelList-implicit",data:e}):n.isValidElement(t)||ee(t)?n.createElement(kw,{key:"labelList-implicit",data:e,content:t}):Zt(t)?n.createElement(kw,ww({data:e},t,{key:"labelList-implicit"})):null:null}(e.label,r)].concat(bw(i)):i};var Cw=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/Kg,f=u?o:o+i*s,p=u?o-i*s:o;return{center:Jg(e,r,l,f),circleTangency:Jg(e,r,n,f),lineTangency:Jg(e,r,l*Math.cos(s*Kg),p),theta:s}},Dw=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=function(t,e){return Sn(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),c=i+a,u=Jg(e,r,o,i),l=Jg(e,r,o,c),s="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(i>c),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var f=Jg(e,r,n,i),p=Jg(e,r,n,c);s+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(i<=c),",\n            ").concat(f.x,",").concat(f.y," Z")}else s+="L ".concat(e,",").concat(r," Z");return s},Iw={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Nw=function(t){var e=_w(_w({},Iw),t),r=e.cx,o=e.cy,i=e.innerRadius,a=e.outerRadius,c=e.cornerRadius,u=e.forceCornerRadius,l=e.cornerIsExternal,s=e.startAngle,f=e.endAngle,p=e.className;if(a<i||s===f)return null;var h,y=jt("recharts-sector",p),d=a-i,v=_n(c,d,0,!0);return h=v>0&&Math.abs(s-f)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=Sn(l-u),f=Cw({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,y=f.theta,d=Cw({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=d.circleTangency,m=d.lineTangency,b=d.theta,g=c?Math.abs(u-l):Math.abs(u-l)-y-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):Dw({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var w="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var x=Cw({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),O=x.circleTangency,j=x.lineTangency,S=x.theta,P=Cw({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),k=P.circleTangency,A=P.lineTangency,E=P.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-E;if(M<0&&0===i)return"".concat(w,"L").concat(e,",").concat(r,"Z");w+="L".concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(e,",").concat(r,"Z");return w}({cx:r,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(v,d/2),forceCornerRadius:u,cornerIsExternal:l,startAngle:s,endAngle:f}):Dw({cx:r,cy:o,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f}),n.createElement("path",Ew({},ro(e,!0),{className:y,d:h,role:"img"}))};function Bw(t){return(Bw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Lw(){return Lw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Lw.apply(this,arguments)}function Rw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function zw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Rw(Object(r),!0).forEach(function(e){Uw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Uw(t,e,r){var n;return n=function(t,e){if("object"!=Bw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Bw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Bw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var $w={curveBasisClosed:function(t){return new Ei(t)},curveBasisOpen:function(t){return new Mi(t)},curveBasis:function(t){return new Ai(t)},curveBumpX:function(t){return new ci(t,!0)},curveBumpY:function(t){return new ci(t,!1)},curveLinearClosed:function(t){return new _i(t)},curveLinear:ri,curveMonotoneX:function(t){return new Ni(t)},curveMonotoneY:function(t){return new Bi(t)},curveNatural:function(t){return new Ri(t)},curveStep:function(t){return new Ui(t,.5)},curveStepAfter:function(t){return new Ui(t,1)},curveStepBefore:function(t){return new Ui(t,0)}},qw=function(t){return t.x===+t.x&&t.y===+t.y},Fw=function(t){return t.x},Ww=function(t){return t.y},Hw=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,c=t.layout,u=t.connectNulls,l=void 0!==u&&u,s=function(t,e){if(ee(t))return t;var r="curve".concat(Uo(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?$w[r]||ri:$w["".concat(r).concat("vertical"===e?"Y":"X")]}(n,c),f=l?i.filter(function(t){return qw(t)}):i;if(Array.isArray(a)){var p=l?a.filter(function(t){return qw(t)}):a,h=f.map(function(t,e){return zw(zw({},t),{},{base:p[e]})});return(e="vertical"===c?ai().y(Ww).x1(Fw).x0(function(t){return t.base.x}):ai().x(Fw).y1(Ww).y0(function(t){return t.base.y})).defined(qw).curve(s),e(h)}return(e="vertical"===c&&kn(a)?ai().y(Ww).x1(Fw).x0(a):kn(a)?ai().x(Fw).y1(Ww).y0(a):ii().x(Fw).y(Ww)).defined(qw).curve(s),e(f)},Vw=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if(!(r&&r.length||o))return null;var a=r&&r.length?Hw(t):o;return n.createElement("path",Lw({},ro(t,!1),$n(t),{className:jt("recharts-curve",e),d:a,ref:i}))},Xw={exports:{}};function Gw(){}function Yw(){}Yw.resetWarningCache=Gw;Xw.exports=function(){function t(t,e,r,n,o,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:Yw,resetWarningCache:Gw};return r.PropTypes=r,r}();const Kw=r(Xw.exports);var Zw=Object.getOwnPropertyNames,Jw=Object.getOwnPropertySymbols,Qw=Object.prototype.hasOwnProperty;function tx(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function ex(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function rx(t){return Zw(t).concat(Jw(t))}var nx=Object.hasOwn||function(t,e){return Qw.call(t,e)};function ox(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var ix=Object.getOwnPropertyDescriptor,ax=Object.keys;function cx(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function ux(t,e){return ox(t.getTime(),e.getTime())}function lx(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function sx(t,e){return t===e}function fx(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),c=t.entries(),u=0;(o=c.next())&&!o.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;)if(a[f])f++;else{var p=o.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}var px=ox;function hx(t,e,r){var n=ax(t),o=n.length;if(ax(e).length!==o)return!1;for(;o-- >0;)if(!wx(t,e,r,n[o]))return!1;return!0}function yx(t,e,r){var n,o,i,a=rx(t),c=a.length;if(rx(e).length!==c)return!1;for(;c-- >0;){if(!wx(t,e,r,n=a[c]))return!1;if(o=ix(t,n),i=ix(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function dx(t,e){return ox(t.valueOf(),e.valueOf())}function vx(t,e){return t.source===e.source&&t.flags===e.flags}function mx(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),c=t.values();(o=c.next())&&!o.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(o.value,i.value,o.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function bx(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function gx(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function wx(t,e,r,n){return!("_owner"!==n&&"__o"!==n&&"__v"!==n||!t.$$typeof&&!e.$$typeof)||nx(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var xx=Array.isArray,Ox="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,jx=Object.assign,Sx=Object.prototype.toString.call.bind(Object.prototype.toString);var Px=kx();function kx(t){void 0===t&&(t={});var e,r=t.circular,n=void 0!==r&&r,o=t.createInternalComparator,i=t.createState,a=t.strict,c=void 0!==a&&a,u=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?yx:cx,areDatesEqual:ux,areErrorsEqual:lx,areFunctionsEqual:sx,areMapsEqual:n?tx(fx,yx):fx,areNumbersEqual:px,areObjectsEqual:n?yx:hx,arePrimitiveWrappersEqual:dx,areRegExpsEqual:vx,areSetsEqual:n?tx(mx,yx):mx,areTypedArraysEqual:n?yx:bx,areUrlsEqual:gx};if(r&&(o=jx({},o,r(o))),e){var i=ex(o.areArraysEqual),a=ex(o.areMapsEqual),c=ex(o.areObjectsEqual),u=ex(o.areSetsEqual);o=jx({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t),l=function(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areErrorsEqual,o=t.areFunctionsEqual,i=t.areMapsEqual,a=t.areNumbersEqual,c=t.areObjectsEqual,u=t.arePrimitiveWrappersEqual,l=t.areRegExpsEqual,s=t.areSetsEqual,f=t.areTypedArraysEqual,p=t.areUrlsEqual;return function(t,h,y){if(t===h)return!0;if(null==t||null==h)return!1;var d=typeof t;if(d!==typeof h)return!1;if("object"!==d)return"number"===d?a(t,h,y):"function"===d&&o(t,h,y);var v=t.constructor;if(v!==h.constructor)return!1;if(v===Object)return c(t,h,y);if(xx(t))return e(t,h,y);if(null!=Ox&&Ox(t))return f(t,h,y);if(v===Date)return r(t,h,y);if(v===RegExp)return l(t,h,y);if(v===Map)return i(t,h,y);if(v===Set)return s(t,h,y);var m=Sx(t);return"[object Date]"===m?r(t,h,y):"[object RegExp]"===m?l(t,h,y):"[object Map]"===m?i(t,h,y):"[object Set]"===m?s(t,h,y):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof h.then&&c(t,h,y):"[object URL]"===m?p(t,h,y):"[object Error]"===m?n(t,h,y):"[object Arguments]"===m?c(t,h,y):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&u(t,h,y)}}(u);return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache,l=void 0===u?e?new WeakMap:void 0:u,s=c.meta;return r(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:n,comparator:l,createState:i,equals:o?o(l):(e=l,function(t,r,n,o,i,a,c){return e(t,r,c)}),strict:c})}function Ax(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){r<0&&(r=o),o-r>e?(t(o),r=-1):function(t){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(t)}(n)})}function Ex(t){return(Ex="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Mx(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return _x(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _x(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Tx(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var o=Mx(n),i=o[0],a=o.slice(1);return"number"==typeof i?void Ax(r.bind(null,a),i):(r(i),void Ax(r.bind(null,a)))}"object"===Ex(n)&&t(n),"function"==typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function Cx(t){return(Cx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Dx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ix(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Dx(Object(r),!0).forEach(function(e){Nx(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Nx(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Cx(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Cx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Cx(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}kx({strict:!0}),kx({circular:!0}),kx({circular:!0,strict:!0}),kx({createInternalComparator:function(){return ox}}),kx({strict:!0,createInternalComparator:function(){return ox}}),kx({circular:!0,createInternalComparator:function(){return ox}}),kx({circular:!0,createInternalComparator:function(){return ox},strict:!0});var Bx=function(t){return t},Lx=function(t,e){return Object.keys(e).reduce(function(r,n){return Ix(Ix({},r),{},Nx({},n,t(n,e[n])))},{})},Rx=function(t,e,r){return t.map(function(t){return"".concat((n=t,n.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())}))," ").concat(e,"ms ").concat(r);var n}).join(",")};function zx(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||$x(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ux(t){return function(t){if(Array.isArray(t))return qx(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||$x(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $x(t,e){if(t){if("string"==typeof t)return qx(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?qx(t,e):void 0}}function qx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Fx=1e-4,Wx=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Hx=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},Vx=function(t,e){return function(r){var n=Wx(t,e);return Hx(n,r)}},Xx=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,o=0,i=1,a=1;break;case"ease":n=.25,o=.1,i=.25,a=1;break;case"ease-in":n=.42,o=0,i=1,a=1;break;case"ease-out":n=.42,o=0,i=.58,a=1;break;case"ease-in-out":n=0,o=0,i=.58,a=1;break;default:var c=e[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u=zx(c[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}),4);n=u[0],o=u[1],i=u[2],a=u[3]}}var l,s,f=Vx(n,i),p=Vx(o,a),h=(l=n,s=i,function(t){var e=Wx(l,s),r=[].concat(Ux(e.map(function(t,e){return t*e}).slice(1)),[0]);return Hx(r,t)}),y=function(t){return t>1?1:t<0?0:t},d=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o=f(r)-e,i=h(r);if(Math.abs(o-e)<Fx||i<Fx)return p(r);r=y(r-o/i)}return p(r)};return d.isStepper=!1,d},Gx=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Xx(n);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return Math.abs(c-e)<Fx&&Math.abs(i)<Fx?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c}();default:if("cubic-bezier"===n.split("(")[0])return Xx(n)}return"function"==typeof n?n:null};function Yx(t){return(Yx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Kx(t){return function(t){if(Array.isArray(t))return rO(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||eO(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Jx(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Zx(Object(r),!0).forEach(function(e){Qx(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Qx(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Yx(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Yx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Yx(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tO(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||eO(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eO(t,e){if(t){if("string"==typeof t)return rO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?rO(t,e):void 0}}function rO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var nO=function(t,e,r){return t+(e-t)*r},oO=function(t){return t.from!==t.to},iO=function t(e,r,n){var o=Lx(function(t,r){if(oO(r)){var n=tO(e(r.from,r.to,r.velocity),2),o=n[0],i=n[1];return Jx(Jx({},r),{},{from:o,velocity:i})}return r},r);return n<1?Lx(function(t,e){return oO(e)?Jx(Jx({},e),{},{velocity:nO(e.velocity,o[t].velocity,n),from:nO(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};const aO=function(t,e,r,n,o){var i,a,c,u,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})})),s=l.reduce(function(r,n){return Jx(Jx({},r),{},Qx({},n,[t[n],e[n]]))},{}),f=l.reduce(function(r,n){return Jx(Jx({},r),{},Qx({},n,{from:t[n],velocity:0,to:e[n]}))},{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){c||(c=n);var i=(n-c)/r.dt;f=iO(r,f,i),o(Jx(Jx(Jx({},t),e),Lx(function(t,e){return e.from},f))),c=n,Object.values(f).filter(oO).length&&(p=requestAnimationFrame(h))}:function(i){u||(u=i);var a=(i-u)/n,c=Lx(function(t,e){return nO.apply(void 0,Kx(e).concat([r(a)]))},s);if(o(Jx(Jx(Jx({},t),e),c)),a<1)p=requestAnimationFrame(h);else{var l=Lx(function(t,e){return nO.apply(void 0,Kx(e).concat([r(1)]))},s);o(Jx(Jx(Jx({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function cO(t){return(cO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var uO=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function lO(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function sO(t){return function(t){if(Array.isArray(t))return fO(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return fO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fO(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pO(Object(r),!0).forEach(function(e){yO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function yO(t,e,r){return(e=vO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dO(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,vO(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function vO(t){var e=function(t,e){if("object"!==cO(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==cO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===cO(e)?e:String(e)}function mO(t,e){return(mO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function bO(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var r,n=xO(t);if(e){var o=xO(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return gO(this,r)}}function gO(t,e){if(e&&("object"===cO(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return wO(t)}function wO(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function xO(t){return(xO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var OO=function(){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&mO(t,e)}(r,t.PureComponent);var e=bO(r);function r(t,n){var o;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r);var i=(o=e.call(this,t,n)).props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(o.handleStyleChange=o.handleStyleChange.bind(wO(o)),o.changeStyle=o.changeStyle.bind(wO(o)),!a||p<=0)return o.state={style:{}},"function"==typeof f&&(o.state={style:l}),gO(o);if(s&&s.length)o.state={style:s[0].style};else if(u){if("function"==typeof f)return o.state={style:u},gO(o);o.state={style:c?yO({},c,u):u}}else o.state={style:{}};return o}return dO(r,[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n)if(r){if(!(Px(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?c:t.to;if(this.state&&u){var f={style:o?yO({},o,s):s};(o&&u[o]!==s||!o&&u!==s)&&this.setState(f)}this.runAnimation(hO(hO({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?yO({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=aO(r,n,Gx(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration,u=void 0===c?0:c;return this.manager.start([o].concat(sO(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(sO(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=Rx(p,i,c),y=hO(hO(hO({},f.style),u),{},{transition:h});return[].concat(sO(t),[y,i,s]).filter(Bx)},[a,Math.max(u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=Tx());var e=t.begin,r=t.duration,n=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,c=t.onAnimationEnd,u=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!=typeof i&&"function"!=typeof l&&"spring"!==i)if(u.length>1)this.runStepAnimation(t);else{var f=n?yO({},n,o):o,p=Rx(Object.keys(f),r,i);s.start([a,e,hO(hO({},f),{},{transition:p}),r,c])}else this.runJSAnimation(t)}},{key:"render",value:function(){var e=this.props,r=e.children;e.begin;var o=e.duration;e.attributeName,e.easing;var i=e.isActive;e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart;var a=lO(e,uO),c=t.Children.count(r),u=this.state.style;if("function"==typeof r)return r(u);if(!i||0===c||o<=0)return r;var l=function(e){var r=e.props,n=r.style,o=void 0===n?{}:n,i=r.className;return t.cloneElement(e,hO(hO({},a),{},{style:hO(hO({},o),u),className:i}))};return 1===c?l(t.Children.only(r)):n.createElement("div",null,t.Children.map(r,function(t){return l(t)}))}}]),r}();function jO(t){return(jO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function SO(){return SO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},SO.apply(this,arguments)}function PO(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return kO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kO(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function AO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function EO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?AO(Object(r),!0).forEach(function(e){MO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):AO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function MO(t,e,r){var n;return n=function(t,e){if("object"!=jO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=jO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==jO(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}OO.displayName="Animate",OO.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},OO.propTypes={from:Kw.oneOfType([Kw.object,Kw.string]),to:Kw.oneOfType([Kw.object,Kw.string]),attributeName:Kw.string,duration:Kw.number,begin:Kw.number,easing:Kw.oneOfType([Kw.string,Kw.func]),steps:Kw.arrayOf(Kw.shape({duration:Kw.number.isRequired,style:Kw.object.isRequired,easing:Kw.oneOfType([Kw.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),Kw.func]),properties:Kw.arrayOf("string"),onAnimationEnd:Kw.func})),children:Kw.oneOfType([Kw.node,Kw.func]),isActive:Kw.bool,canBegin:Kw.bool,onAnimationEnd:Kw.func,shouldReAnimate:Kw.bool,onAnimationStart:Kw.func,onAnimationReStart:Kw.func};var _O=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},TO=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},CO={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},DO=function(e){var r=EO(EO({},CO),e),o=t.useRef(),i=PO(t.useState(-1),2),a=i[0],c=i[1];t.useEffect(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&c(t)}catch(e){}},[]);var u=r.x,l=r.y,s=r.width,f=r.height,p=r.radius,h=r.className,y=r.animationEasing,d=r.animationDuration,v=r.animationBegin,m=r.isAnimationActive,b=r.isUpdateAnimationActive;if(u!==+u||l!==+l||s!==+s||f!==+f||0===s||0===f)return null;var g=jt("recharts-rectangle",h);return b?n.createElement(OO,{canBegin:a>0,from:{width:s,height:f,x:u,y:l},to:{width:s,height:f,x:u,y:l},duration:d,animationEasing:y,isActive:b},function(t){var e=t.width,i=t.height,c=t.x,u=t.y;return n.createElement(OO,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:d,isActive:m,easing:y},n.createElement("path",SO({},ro(r,!0),{className:g,d:_O(c,u,e,i,p),ref:o})))}):n.createElement("path",SO({},ro(r,!0),{className:g,d:_O(u,l,s,f,p)}))};function IO(){return IO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},IO.apply(this,arguments)}var NO=function(t){var e=t.cx,r=t.cy,o=t.r,i=jt("recharts-dot",t.className);return e===+e&&r===+r&&o===+o?n.createElement("circle",IO({},ro(t,!1),$n(t),{className:i,cx:e,cy:r,r:o})):null};function BO(t){return(BO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var LO=["x","y","top","left","width","height","className"];function RO(){return RO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},RO.apply(this,arguments)}function zO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function UO(t,e,r){var n;return n=function(t,e){if("object"!=BO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=BO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==BO(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $O(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var qO=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},FO=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,a=t.top,c=void 0===a?0:a,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,y=t.className,d=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zO(Object(r),!0).forEach(function(e){UO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},$O(t,LO));return kn(r)&&kn(i)&&kn(f)&&kn(h)&&kn(c)&&kn(l)?n.createElement("path",RO({},ro(d,!0),{className:jt("recharts-cross",y),d:qO(r,i,f,h,c,l)})):null},WO=Wc(Object.getPrototypeOf,Object),HO=Ut,VO=WO,XO=$t,GO=Function.prototype,YO=Object.prototype,KO=GO.toString,ZO=YO.hasOwnProperty,JO=KO.call(Object);const QO=r(function(t){if(!XO(t)||"[object Object]"!=HO(t))return!1;var e=VO(t);if(null===e)return!0;var r=ZO.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&KO.call(r)==JO});var tj=Ut,ej=$t;const rj=r(function(t){return!0===t||!1===t||ej(t)&&"[object Boolean]"==tj(t)});function nj(t){return(nj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function oj(){return oj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},oj.apply(this,arguments)}function ij(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return aj(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aj(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function cj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cj(Object(r),!0).forEach(function(e){lj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lj(t,e,r){var n;return n=function(t,e){if("object"!=nj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var sj=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},fj={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},pj=function(e){var r=uj(uj({},fj),e),o=t.useRef(),i=ij(t.useState(-1),2),a=i[0],c=i[1];t.useEffect(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&c(t)}catch(e){}},[]);var u=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,h=r.className,y=r.animationEasing,d=r.animationDuration,v=r.animationBegin,m=r.isUpdateAnimationActive;if(u!==+u||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var b=jt("recharts-trapezoid",h);return m?n.createElement(OO,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:u,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:u,y:l},duration:d,animationEasing:y,isActive:m},function(t){var e=t.upperWidth,i=t.lowerWidth,c=t.height,u=t.x,l=t.y;return n.createElement(OO,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:d,easing:y},n.createElement("path",oj({},ro(r,!0),{className:b,d:sj(u,l,e,i,c),ref:o})))}):n.createElement("g",null,n.createElement("path",oj({},ro(r,!0),{className:b,d:sj(u,l,s,f,p)})))},hj=["option","shapeType","propTransformer","activeClassName","isActive"];function yj(t){return(yj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dj(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function vj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function mj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vj(Object(r),!0).forEach(function(e){bj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function bj(t,e,r){var n;return n=function(t,e){if("object"!=yj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==yj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gj(t,e){return mj(mj({},e),t)}function wj(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(DO,r);case"trapezoid":return n.createElement(pj,r);case"sector":return n.createElement(Nw,r);case"symbols":if(function(t){return"symbols"===t}(e))return n.createElement(ta,r);break;default:return null}}function xj(e){var r,o=e.option,i=e.shapeType,a=e.propTransformer,c=void 0===a?gj:a,u=e.activeClassName,l=void 0===u?"recharts-active-shape":u,s=e.isActive,f=dj(e,hj);if(t.isValidElement(o))r=t.cloneElement(o,mj(mj({},f),function(e){return t.isValidElement(e)?e.props:e}(o)));else if(ee(o))r=o(f);else if(QO(o)&&!rj(o)){var p=c(o,f);r=n.createElement(wj,{shapeType:i,elementProps:p})}else{var h=f;r=n.createElement(wj,{shapeType:i,elementProps:h})}return s?n.createElement(ho,{className:l},r):r}function Oj(t,e){return null!=e&&"trapezoids"in t.props}function jj(t,e){return null!=e&&"sectors"in t.props}function Sj(t,e){return null!=e&&"points"in t.props}function Pj(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function kj(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function Aj(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function Ej(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return Oj(t,e)?r="trapezoids":jj(t,e)?r="sectors":Sj(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return Oj(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:jj(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:Sj(t,e)?e.payload:{}}(r,e),a=n.filter(function(t,n){var a=Jm(i,t),c=r.props[o].filter(function(t){var n=function(t,e){var r;return Oj(t,e)?r=Pj:jj(t,e)?r=kj:Sj(t,e)&&(r=Aj),r}(r,e);return n(t,e)}),u=r.props[o].indexOf(c[c.length-1]);return a&&n===u});return n.indexOf(a[a.length-1])}var Mj=Math.ceil,_j=Math.max;var Tj=Qf,Cj=1/0;var Dj=function(t){return t?(t=Tj(t))===Cj||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0},Ij=function(t,e,r,n){for(var o=-1,i=_j(Mj((e-t)/(r||1)),0),a=Array(i);i--;)a[n?i:++o]=t,t+=r;return a},Nj=Zs,Bj=Dj;const Lj=r(function(t){return function(e,r,n){return n&&"number"!=typeof n&&Nj(e,r,n)&&(r=n=void 0),e=Bj(e),void 0===r?(r=e,e=0):r=Bj(r),n=void 0===n?e<r?1:-1:Bj(n),Ij(e,r,n,t)}}());function Rj(t){return(Rj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function zj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Uj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zj(Object(r),!0).forEach(function(e){$j(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function $j(t,e,r){var n;return n=function(t,e){if("object"!=Rj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Rj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Rj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var qj=["Webkit","Moz","O","ms"];function Fj(t){return(Fj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Wj(){return Wj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Wj.apply(this,arguments)}function Hj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Vj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Hj(Object(r),!0).forEach(function(e){Jj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Hj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Xj(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Qj(n.key),n)}}function Gj(t,e,r){return e=Kj(e),function(t,e){if(e&&("object"===Fj(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Yj()?Reflect.construct(e,r||[],Kj(t).constructor):e.apply(t,r))}function Yj(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Yj=function(){return!!t})()}function Kj(t){return(Kj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Zj(t,e){return(Zj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Jj(t,e,r){return(e=Qj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qj(t){var e=function(t,e){if("object"!=Fj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Fj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Fj(e)?e:e+""}var tS=function(t){return t.changedTouches&&!!t.changedTouches.length},eS=function(){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Jj(r=Gj(this,e,[t]),"handleDrag",function(t){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(t):r.state.isSlideMoving&&r.handleSlideDrag(t)}),Jj(r,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleDrag(t.changedTouches[0])}),Jj(r,"handleDragEnd",function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=r.props,e=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:e,startIndex:o})}),r.detachDragEndListener()}),Jj(r,"handleLeaveWrapper",function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))}),Jj(r,"handleEnterSlideOrTraveller",function(){r.setState({isTextActive:!0})}),Jj(r,"handleLeaveSlideOrTraveller",function(){r.setState({isTextActive:!1})}),Jj(r,"handleSlideDragStart",function(t){var e=tS(t)?t.changedTouches[0]:t;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),r.attachDragEndListener()}),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Zj(t,e)}(e,t.PureComponent),r=e,i=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,c=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+o-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return n.isValidElement(t)?n.cloneElement(t,r):ee(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return Vj({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=Ah().domain(Lj(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}}({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],(o=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(r,n),l=Math.max(r,n),s=e.getIndexInRange(o,u),f=e.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=Sg(r[t],o,t);return ee(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=tS(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,y={startX:this.state.startX,endX:this.state.endX},d=t.pageX-r;d>0?d=Math.min(d,u+l-s-a):d<0&&(d=Math.max(d,u-a)),y[n]=a+d;var v=this.getIndex(y),m=v.startIndex,b=v.endIndex;this.setState(Jj(Jj({},n,a+d),"brushMoveStartX",t.pageX),function(){var t;f&&(t=h.length-1,("startX"===n&&(o>i?m%p===0:b%p===0)||o<i&&b===t||"endX"===n&&(o>i?b%p===0:m%p===0)||o>i&&b===t)&&f(v))})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(Jj({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var e=this.props,r=e.x,o=e.y,i=e.width,a=e.height,c=e.data,u=e.children,l=e.padding,s=t.Children.only(u);return s?n.cloneElement(s,{x:r,y:o,width:i,height:a,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,r){var o,i,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,y=c.startIndex,d=c.endIndex,v=Math.max(t,this.props.x),m=Vj(Vj({},ro(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(o=h[y])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=h[d])||void 0===i?void 0:i.name);return n.createElement(ho,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,r))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return n.createElement(ho,{className:"recharts-brush-texts"},n.createElement(Jp,Wj({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),n.createElement(Jp,Wj({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,i=t.x,a=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,y=s.isSlideMoving,d=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!kn(i)||!kn(a)||!kn(c)||!kn(u)||c<=0||u<=0)return null;var m,b,g,w,x=jt("recharts-brush",r),O=1===n.Children.count(o),j=(b="none",g=(m="userSelect").replace(/(\w)/,function(t){return t.toUpperCase()}),(w=qj.reduce(function(t,e){return Uj(Uj({},t),{},$j({},e+g,b))},{}))[m]=b,w);return n.createElement(ho,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:j},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||y||d||v||l)&&this.renderText())}}])&&Xj(r.prototype,o),i&&Xj(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();Jj(eS,"displayName","Brush"),Jj(eS,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var rS=ms;var nS=_a,oS=kl,iS=function(t,e){var r;return rS(t,function(t,n,o){return!(r=e(t,n,o))}),!!r},aS=St,cS=Zs;const uS=r(function(t,e,r){var n=aS(t)?nS:iS;return r&&cS(t,e,r)&&(e=void 0),n(t,oS(e))});var lS=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sS=Rs;var fS=function(t,e,r){"__proto__"==e&&sS?sS(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r},pS=ds,hS=kl;const yS=r(function(t,e){var r={};return e=hS(e),pS(t,function(t,n,o){fS(r,n,e(t,n,o))}),r});var dS=ms;var vS=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0},mS=function(t,e){var r=!0;return dS(t,function(t,n,o){return r=!!e(t,n,o)}),r},bS=kl,gS=St,wS=Zs;const xS=r(function(t,e,r){var n=gS(t)?vS:mS;return r&&wS(t,e,r)&&(e=void 0),n(t,bS(e))});var OS=["x","y"];function jS(t){return(jS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function SS(){return SS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},SS.apply(this,arguments)}function PS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function kS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?PS(Object(r),!0).forEach(function(e){AS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):PS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function AS(t,e,r){var n;return n=function(t,e){if("object"!=jS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=jS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==jS(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ES(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function MS(t,e){var r=t.x,n=t.y,o=ES(t,OS),i="".concat(r),a=parseInt(i,10),c="".concat(n),u=parseInt(c,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return kS(kS(kS(kS(kS({},e),o),a?{x:a}:{}),u?{y:u}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function _S(t){return n.createElement(xj,SS({shapeType:"rectangle",propTransformer:MS,activeClassName:"recharts-active-bar"},t))}var TS,CS=["value","background"];function DS(t){return(DS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function IS(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function NS(){return NS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},NS.apply(this,arguments)}function BS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function LS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?BS(Object(r),!0).forEach(function(e){FS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):BS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function RS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,WS(n.key),n)}}function zS(t,e,r){return e=$S(e),function(t,e){if(e&&("object"===DS(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,US()?Reflect.construct(e,r||[],$S(t).constructor):e.apply(t,r))}function US(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(US=function(){return!!t})()}function $S(t){return($S=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function qS(t,e){return(qS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function FS(t,e,r){return(e=WS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function WS(t){var e=function(t,e){if("object"!=DS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=DS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==DS(e)?e:e+""}var HS=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return FS(t=zS(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),FS(t,"id",Mn("recharts-bar-")),FS(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),FS(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&qS(t,e)}(e,t.PureComponent),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(o=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,c=r.activeBar,u=ro(this.props,!1);return t&&t.map(function(t,r){var l=r===a,s=l?c:o,f=LS(LS(LS({},u),t),{},{isActive:l,option:s,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(ho,NS({className:"recharts-bar-rectangle"},qn(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),n.createElement(_S,f))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,i=e.isAnimationActive,a=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return n.createElement(OO,{begin:a,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=s&&s[e];if(r){var n=Cn(r.x,t.x),a=Cn(r.y,t.y),c=Cn(r.width,t.width),u=Cn(r.height,t.height);return LS(LS({},t),{},{x:n(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===o){var l=Cn(0,t.height)(i);return LS(LS({},t),{},{y:t.y+t.height-l,height:l})}var f=Cn(0,t.width)(i);return LS(LS({},t),{},{width:f})});return n.createElement(ho,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&Jm(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=ro(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=IS(e,CS);if(!c)return null;var l=LS(LS(LS(LS(LS({},u),{},{fill:"#eee"},c),a),qn(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(_S,NS({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,c=r.layout,u=Jn(r.children,pg);if(!u)return null;var l="vertical"===c?o[0].height/2:o[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:Sg(t,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(ho,f,u.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,o=t.className,i=t.xAxis,a=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,d=jt("recharts-bar",o),v=i&&i.allowDataOverflow,m=a&&a.allowDataOverflow,b=v||m,g=Xr(h)?this.id:h;return n.createElement(ho,{className:d},v||m?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(g)},n.createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,n.createElement(ho,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||y)&&kw.renderCallByParent(this.props,r))}}])&&RS(r.prototype,o),i&&RS(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();function VS(t){return(VS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function XS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ZS(n.key),n)}}function GS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function YS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?GS(Object(r),!0).forEach(function(e){KS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):GS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function KS(t,e,r){return(e=ZS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ZS(t){var e=function(t,e){if("object"!=VS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=VS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==VS(e)?e:e+""}TS=HS,FS(HS,"displayName","Bar"),FS(HS,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Mf.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),FS(HS,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null}(n,r);if(!h)return null;var y=e.layout,d=r.type.defaultProps,v=void 0!==d?LS(LS({},d),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,w="horizontal"===y?a:i,x=l?w.scale.domain():null,O=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]}({numericAxis:w}),j=Jn(b,dp),S=f.map(function(t,e){var n,f,p,d,v,b;l?n=function(t,e){if(!e||2!==e.length||!kn(e[0])||!kn(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!kn(t[0])||t[0]<r)&&(o[0]=r),(!kn(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o}(l[s+e],x):(n=Sg(t,m),Array.isArray(n)||(n=[O,n]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||Jb(),e)}}(g,TS.defaultProps.minPointSize)(n[1],e);if("horizontal"===y){var S,P=[a.scale(n[0]),a.scale(n[1])],k=P[0],A=P[1];f=Rg({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),p=null!==(S=null!=A?A:k)&&void 0!==S?S:void 0,d=h.size;var E=k-A;if(v=Number.isNaN(E)?0:E,b={x:f,y:a.y,width:d,height:a.height},Math.abs(w)>0&&Math.abs(v)<Math.abs(w)){var M=Sn(v||w)*(Math.abs(w)-Math.abs(v));p-=M,v+=M}}else{var _=[i.scale(n[0]),i.scale(n[1])],T=_[0],C=_[1];if(f=T,p=Rg({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),d=C-T,v=h.size,b={x:i.x,y:p,width:i.width,height:v},Math.abs(w)>0&&Math.abs(d)<Math.abs(w))d+=Sn(d||w)*(Math.abs(w)-Math.abs(d))}return LS(LS(LS({},t),{},{x:f,y:p,width:d,height:v,value:l?n:n[1],payload:t,background:b},j&&j[e]&&j[e].props),{},{tooltipPayload:[Hg(r,t)],tooltipPosition:{x:f+d/2,y:p+v/2}})});return LS({data:S,layout:y},p)});var JS=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!Qn(u,HS);return l.reduce(function(i,a){var u,l,p,h,y,d=e[a],v=d.orientation,m=d.domain,b=d.padding,g=void 0===b?{}:b,w=d.mirror,x=d.reversed,O="".concat(v).concat(w?"Mirror":"");if("number"===d.type&&("gap"===d.padding||"no-gap"===d.padding)){var j=m[1]-m[0],S=1/0,P=d.categoricalDomain.sort(In);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var k=S/j,A="vertical"===d.layout?r.height:r.width;if("gap"===d.padding&&(u=k*A/2),"no-gap"===d.padding){var E=_n(t.barCategoryGap,k*A),M=k*A/2;u=M-E-(M-E)/A*E}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:d.range,x&&(l=[l[1],l[0]]);var _=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:Ph(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:Gy(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:Ah(),realScaleType:"point"}:"category"===o?{scale:Ph(),realScaleType:"band"}:{scale:Gy(),realScaleType:"linear"};if(Zr(n)){var c="scale".concat(Uo(n));return{scale:(Dm[c]||Ah)(),realScaleType:Dm[c]?c:"point"}}return ee(n)?{scale:n}:{scale:Ah(),realScaleType:"point"}}(d,o,f),T=_.scale,C=_.realScaleType;T.domain(m).range(l),function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-Ig,i=Math.max(n[0],n[1])+Ig,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}}(T);var D=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=Kb(u,o,a);return t.domain([Fm(l),zm(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:Zb(s,o,a)}}return null}(T,YS(YS({},d),{},{realScaleType:C}));"xAxis"===n?(y="top"===v&&!w||"bottom"===v&&w,p=r.left,h=s[O]-y*d.height):"yAxis"===n&&(y="left"===v&&!w||"right"===v&&w,p=s[O]-y*d.width,h=r.top);var I=YS(YS(YS({},d),D),{},{realScaleType:C,x:p,y:h,scale:T,width:"xAxis"===n?r.width:d.width,height:"yAxis"===n?r.height:d.height});return I.bandSize=Fg(I,D),d.hide||"xAxis"!==n?d.hide||(s[O]+=(y?-1:1)*I.width):s[O]+=(y?-1:1)*I.height,YS(YS({},i),{},KS({},a,I))},{})},QS=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},tP=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&XS(e.prototype,r),n&&XS(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();KS(tP,"EPS",1e-4);var eP=function(t){var e=Object.keys(t).reduce(function(e,r){return YS(YS({},e),{},KS({},r,tP.create(t[r])))},{});return YS(YS({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return yS(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return xS(t,function(t,r){return e[r].isInRange(t)})}})};var rP=kl,nP=Zc,oP=eu;var iP=function(t){return function(e,r,n){var o=Object(e);if(!nP(e)){var i=rP(r);e=oP(e),r=function(t){return i(o[t],t,o)}}var a=t(e,r,n);return a>-1?o[i?e[a]:a]:void 0}},aP=Dj;var cP=Al,uP=kl,lP=function(t){var e=aP(t),r=e%1;return e==e?r?e-r:e:0},sP=Math.max;const fP=r(iP(function(t,e,r){var n=null==t?0:t.length;if(!n)return-1;var o=null==r?0:lP(r);return o<0&&(o=sP(n+o,0)),cP(t,uP(e),o)}));var pP=wr(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),hP=t.createContext(void 0),yP=t.createContext(void 0),dP=t.createContext(void 0),vP=t.createContext({}),mP=t.createContext(void 0),bP=t.createContext(0),gP=t.createContext(0),wP=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,s=pP(i);return n.createElement(hP.Provider,{value:r},n.createElement(yP.Provider,{value:o},n.createElement(vP.Provider,{value:i},n.createElement(dP.Provider,{value:s},n.createElement(mP.Provider,{value:a},n.createElement(bP.Provider,{value:l},n.createElement(gP.Provider,{value:u},c)))))))},xP=function(e){var r=t.useContext(hP);null==r&&Jb();var n=r[e];return null==n&&Jb(),n},OP=function(e){var r=t.useContext(yP);null==r&&Jb();var n=r[e];return null==n&&Jb(),n},jP=function(){return t.useContext(gP)},SP=function(){return t.useContext(bP)};function PP(t){return(PP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function kP(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,IP(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function AP(t,e,r){return e=MP(e),function(t,e){if(e&&("object"===PP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,EP()?Reflect.construct(e,r||[],MP(t).constructor):e.apply(t,r))}function EP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(EP=function(){return!!t})()}function MP(t){return(MP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function _P(t,e){return(_P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function TP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function CP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?TP(Object(r),!0).forEach(function(e){DP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):TP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function DP(t,e,r){return(e=IP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function IP(t){var e=function(t,e){if("object"!=PP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=PP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==PP(e)?e:e+""}function NP(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return BP(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return BP(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function BP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function LP(){return LP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},LP.apply(this,arguments)}function RP(e){var r=e.x,o=e.y,i=e.segment,a=e.xAxisId,c=e.yAxisId,u=e.shape,l=e.className,s=e.alwaysShow,f=t.useContext(mP),p=xP(a),h=OP(c),y=t.useContext(dP);if(!f||!y)return null;yo(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,y=t.y.apply(h,{position:i});if(lS(u,"discard")&&!t.y.isInRange(y))return null;var d=[{x:l+f,y:y},{x:l,y:y}];return"left"===c?d.reverse():d}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(lS(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return lS(u,"discard")&&uS(g,function(e){return!t.isInRange(e)})?null:g}return null}(eP({x:p.scale,y:h.scale}),An(r),An(o),i&&2===i.length,y,e.position,p.orientation,h.orientation,e);if(!d)return null;var v=NP(d,2),m=v[0],b=m.x,g=m.y,w=v[1],x=w.x,O=w.y,j=CP(CP({clipPath:lS(e,"hidden")?"url(#".concat(f,")"):void 0},ro(e,!0)),{},{x1:b,y1:g,x2:x,y2:O});return n.createElement(ho,{className:jt("recharts-reference-line",l)},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):ee(t)?t(e):n.createElement("line",LP({},e,{className:"recharts-reference-line-line"}))}(u,j),pw.renderCallByParent(e,function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return QS({x:e,y:r},{x:n,y:o})}({x1:b,y1:g,x2:x,y2:O})))}var zP=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),AP(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_P(t,e)}(t,n.Component),kP(t,[{key:"render",value:function(){return n.createElement(RP,this.props)}}])}();function UP(){return UP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},UP.apply(this,arguments)}function $P(t){return($P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function qP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function FP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qP(Object(r),!0).forEach(function(e){YP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function WP(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,KP(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function HP(t,e,r){return e=XP(e),function(t,e){if(e&&("object"===$P(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,VP()?Reflect.construct(e,r||[],XP(t).constructor):e.apply(t,r))}function VP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(VP=function(){return!!t})()}function XP(t){return(XP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function GP(t,e){return(GP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function YP(t,e,r){return(e=KP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function KP(t){var e=function(t,e){if("object"!=$P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=$P(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==$P(e)?e:e+""}DP(zP,"displayName","ReferenceLine"),DP(zP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var ZP=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),HP(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&GP(t,e)}(t,n.Component),WP(t,[{key:"render",value:function(){var e=this.props,r=e.x,o=e.y,i=e.r,a=e.alwaysShow,c=e.clipPathId,u=An(r),l=An(o);if(yo(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=eP({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return lS(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,y=h.shape,d=h.className,v=FP(FP({clipPath:lS(this.props,"hidden")?"url(#".concat(c,")"):void 0},ro(this.props,!0)),{},{cx:f,cy:p});return n.createElement(ho,{className:jt("recharts-reference-dot",d)},t.renderDot(y,v),pw.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}])}();function JP(){return JP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},JP.apply(this,arguments)}function QP(t){return(QP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ek(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tk(Object(r),!0).forEach(function(e){ck(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rk(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uk(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function nk(t,e,r){return e=ik(e),function(t,e){if(e&&("object"===QP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ok()?Reflect.construct(e,r||[],ik(t).constructor):e.apply(t,r))}function ok(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ok=function(){return!!t})()}function ik(t){return(ik=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ak(t,e){return(ak=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ck(t,e,r){return(e=uk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uk(t){var e=function(t,e){if("object"!=QP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=QP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==QP(e)?e:e+""}YP(ZP,"displayName","ReferenceDot"),YP(ZP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),YP(ZP,"renderDot",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):ee(t)?t(e):n.createElement(NO,UP({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var lk=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),nk(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ak(t,e)}(t,n.Component),rk(t,[{key:"render",value:function(){var e=this.props,r=e.x1,o=e.x2,i=e.y1,a=e.y2,c=e.className,u=e.alwaysShow,l=e.clipPathId;yo(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=An(r),f=An(o),p=An(i),h=An(a),y=this.props.shape;if(!(s||f||p||h||y))return null;var d=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=eP({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!lS(o,"discard")||f.isInRange(p)&&f.isInRange(h)?QS(p,h):null}(s,f,p,h,this.props);if(!d&&!y)return null;var v=lS(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(ho,{className:jt("recharts-reference-area",c)},t.renderRect(y,ek(ek({clipPath:v},ro(this.props,!0)),d)),pw.renderCallByParent(this.props,d))}}])}();function sk(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)n.push(t[o]);return n}function fk(t,e,r){return function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=n*Math.PI/180,i=Math.atan(r/e),a=o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o);return Math.abs(a)}({width:t.width+e.width,height:t.height+e.height},r)}function pk(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hk(t){return(hk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yk(Object(r),!0).forEach(function(e){vk(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function vk(t,e,r){var n;return n=function(t,e){if("object"!=hk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hk(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==hk(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mk(t,e,r){var n=t.tick,o=t.ticks,i=t.viewBox,a=t.minTickGap,c=t.orientation,u=t.interval,l=t.tickFormatter,s=t.unit,f=t.angle;if(!o||!o.length||!n)return[];if(kn(u)||Mf.isSsr)return function(t,e){return sk(t,e+1)}(o,"number"==typeof u&&kn(u)?u:0);var p=[],h="top"===c||"bottom"===c?"width":"height",y=s&&"width"===h?jp(s,{fontSize:e,letterSpacing:r}):{width:0,height:0},d=function(t,n){var o=ee(l)?l(t.value,n):t.value;return"width"===h?fk(jp(o,{fontSize:e,letterSpacing:r}),y,f):jp(o,{fontSize:e,letterSpacing:r})[h]},v=o.length>=2?Sn(o[1].coordinate-o[0].coordinate):1,m=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,c=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i}}(i,v,h);return"equidistantPreserveStart"===u?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c,p=function(){var e=null==n?void 0:n[l];if(void 0===e)return{v:sk(n,s)};var i,a=l,p=function(){return void 0===i&&(i=r(e,a)),i},h=e.coordinate,y=0===l||pk(t,h,p,f,u);y||(l=0,f=c,s+=1),y&&(f=h+t*(p()/2+o),l+=s)};s<=a.length;)if(i=p())return i.v;return[]}(v,m,d,o,a):(p="preserveStart"===u||"preserveStartEnd"===u?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=dk(dk({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),pk(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=dk(dk({},s),{},{isShow:!0}))}for(var h=i?c-1:c,y=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=dk(dk({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=dk(dk({},i),{},{tickCoord:i.coordinate});pk(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=dk(dk({},i),{},{isShow:!0}))},d=0;d<h;d++)y(d);return a}(v,m,d,o,a,"preserveStartEnd"===u):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=dk(dk({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=dk(dk({},l),{},{tickCoord:l.coordinate});pk(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=dk(dk({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(v,m,d,o,a),p.filter(function(t){return t.isShow}))}ck(lk,"displayName","ReferenceArea"),ck(lk,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),ck(lk,"renderRect",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):ee(t)?t(e):n.createElement(DO,JP({},e,{className:"recharts-reference-area-rect"}))});var bk=["viewBox"],gk=["viewBox"],wk=["ticks"];function xk(t){return(xk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ok(){return Ok=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ok.apply(this,arguments)}function jk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Sk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jk(Object(r),!0).forEach(function(e){Tk(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Pk(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function kk(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ck(n.key),n)}}function Ak(t,e,r){return e=Mk(e),function(t,e){if(e&&("object"===xk(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ek()?Reflect.construct(e,r||[],Mk(t).constructor):e.apply(t,r))}function Ek(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Ek=function(){return!!t})()}function Mk(t){return(Mk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function _k(t,e){return(_k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Tk(t,e,r){return(e=Ck(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ck(t){var e=function(t,e){if("object"!=xk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=xk(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==xk(e)?e:e+""}var Dk=function(){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=Ak(this,e,[t])).state={fontSize:"",letterSpacing:""},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_k(t,e)}(e,t.Component),r=e,i=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):ee(t)?t(e):n.createElement(Jp,Ok({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(o=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=Pk(t,bk),o=this.props,i=o.viewBox,a=Pk(o,gk);return!Nn(r,i)||!Nn(n,a)||!Nn(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,y=c.mirror,d=c.tickMargin,v=y?-1:1,m=t.tickSize||h,b=kn(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-v*m)-v*d,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!y*s)-v*m)-v*d,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +y*s)+v*m)+v*d,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+v*m)+v*d,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,c=t.mirror,u=t.axisLine,l=Sk(Sk(Sk({},ro(this.props,!1)),ro(u,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var s=+("top"===a&&!c||"bottom"===a&&c);l=Sk(Sk({},l),{},{x1:e,y1:r+s*i,x2:e+o,y2:r+s*i})}else{var f=+("left"===a&&!c||"right"===a&&c);l=Sk(Sk({},l),{},{x1:e+f*o,y1:r,x2:e+f*o,y2:r+i})}return n.createElement("line",Ok({},l,{className:jt("recharts-cartesian-axis-line",Vr(u,"className"))}))}},{key:"renderTicks",value:function(t,r,o){var i=this,a=this.props,c=a.tickLine,u=a.stroke,l=a.tick,s=a.tickFormatter,f=a.unit,p=mk(Sk(Sk({},this.props),{},{ticks:t}),r,o),h=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),d=ro(this.props,!1),v=ro(l,!1),m=Sk(Sk({},d),{},{fill:"none"},ro(c,!1)),b=p.map(function(t,r){var o=i.getTickLineCoord(t),a=o.line,b=o.tick,g=Sk(Sk(Sk(Sk({textAnchor:h,verticalAnchor:y},d),{},{stroke:"none",fill:u},v),b),{},{index:r,payload:t,visibleTicksCount:p.length,tickFormatter:s});return n.createElement(ho,Ok({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},qn(i.props,t,r)),c&&n.createElement("line",Ok({},m,a,{className:jt("recharts-cartesian-axis-tick-line",Vr(c,"className"))})),l&&e.renderTickItem(l,g,"".concat(ee(s)?s(t.value,r):t.value).concat(f||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,i=e.height,a=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=Pk(u,wk),f=l;return ee(a)&&(f=l&&l.length>0?a(this.props):a(s)),o<=0||i<=0||!f||!f.length?null:n.createElement(ho,{className:jt("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),pw.renderCallByParent(this.props))}}])&&kk(r.prototype,o),i&&kk(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();Tk(Dk,"displayName","CartesianAxis"),Tk(Dk,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var Ik=["x1","y1","x2","y2","key"],Nk=["offset"];function Bk(t){return(Bk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Lk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Rk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Lk(Object(r),!0).forEach(function(e){zk(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Lk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function zk(t,e,r){var n;return n=function(t,e){if("object"!=Bk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Bk(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Bk(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Uk(){return Uk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Uk.apply(this,arguments)}function $k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var qk=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.ry;return n.createElement("rect",{x:o,y:i,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function Fk(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(ee(t))r=t(e);else{var o=e.x1,i=e.y1,a=e.x2,c=e.y2,u=e.key,l=$k(e,Ik),s=ro(l,!1);s.offset;var f=$k(s,Nk);r=n.createElement("line",Uk({},f,{x1:o,y1:i,x2:a,y2:c,fill:"none",key:u}))}return r}function Wk(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){var a=Rk(Rk({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o});return Fk(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function Hk(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){var a=Rk(Rk({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o});return Fk(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function Vk(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=!s[u+1]?i+c-t:s[u+1]-t;if(l<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function Xk(t){var e=t.vertical,r=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,c=t.y,u=t.width,l=t.height,s=t.verticalPoints;if(!r||!o||!o.length)return null;var f=s.map(function(t){return Math.round(t+a-a)}).sort(function(t,e){return t-e});a!==f[0]&&f.unshift(0);var p=f.map(function(t,e){var r=!f[e+1]?a+u-t:f[e+1]-t;if(r<=0)return null;var s=e%o.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:c,width:r,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var Gk=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return _g(mk(Rk(Rk(Rk({},Dk.defaultProps),r),{},{ticks:Tg(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},Yk=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return _g(mk(Rk(Rk(Rk({},Dk.defaultProps),r),{},{ticks:Tg(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},Kk={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Zk(e){var r,o,i,a,c,u,l,s,f=jP(),p=SP(),h=t.useContext(vP),y=Rk(Rk({},e),{},{stroke:null!==(r=e.stroke)&&void 0!==r?r:Kk.stroke,fill:null!==(o=e.fill)&&void 0!==o?o:Kk.fill,horizontal:null!==(i=e.horizontal)&&void 0!==i?i:Kk.horizontal,horizontalFill:null!==(a=e.horizontalFill)&&void 0!==a?a:Kk.horizontalFill,vertical:null!==(c=e.vertical)&&void 0!==c?c:Kk.vertical,verticalFill:null!==(u=e.verticalFill)&&void 0!==u?u:Kk.verticalFill,x:kn(e.x)?e.x:h.left,y:kn(e.y)?e.y:h.top,width:kn(e.width)?e.width:h.width,height:kn(e.height)?e.height:h.height}),d=y.x,v=y.y,m=y.width,b=y.height,g=y.syncWithTicks,w=y.horizontalValues,x=y.verticalValues,O=(l=t.useContext(hP),Tn(l)),j=(s=t.useContext(yP),fP(s,function(t){return xS(t.domain,Number.isFinite)})||Tn(s));if(!kn(m)||m<=0||!kn(b)||b<=0||!kn(d)||d!==+d||!kn(v)||v!==+v)return null;var S=y.verticalCoordinatesGenerator||Gk,P=y.horizontalCoordinatesGenerator||Yk,k=y.horizontalPoints,A=y.verticalPoints;if((!k||!k.length)&&ee(P)){var E=w&&w.length,M=P({yAxis:j?Rk(Rk({},j),{},{ticks:E?w:j.ticks}):void 0,width:f,height:p,offset:h},!!E||g);yo(Array.isArray(M),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Bk(M),"]")),Array.isArray(M)&&(k=M)}if((!A||!A.length)&&ee(S)){var _=x&&x.length,T=S({xAxis:O?Rk(Rk({},O),{},{ticks:_?x:O.ticks}):void 0,width:f,height:p,offset:h},!!_||g);yo(Array.isArray(T),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Bk(T),"]")),Array.isArray(T)&&(A=T)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(qk,{fill:y.fill,fillOpacity:y.fillOpacity,x:y.x,y:y.y,width:y.width,height:y.height,ry:y.ry}),n.createElement(Wk,Uk({},y,{offset:h,horizontalPoints:k,xAxis:O,yAxis:j})),n.createElement(Hk,Uk({},y,{offset:h,verticalPoints:A,xAxis:O,yAxis:j})),n.createElement(Vk,Uk({},y,{horizontalPoints:k})),n.createElement(Xk,Uk({},y,{verticalPoints:A})))}Zk.displayName="CartesianGrid";var Jk=["type","layout","connectNulls","ref"],Qk=["key"];function tA(t){return(tA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function rA(){return rA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},rA.apply(this,arguments)}function nA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function oA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nA(Object(r),!0).forEach(function(e){pA(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function iA(t){return function(t){if(Array.isArray(t))return aA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return aA(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aA(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function cA(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hA(n.key),n)}}function uA(t,e,r){return e=sA(e),function(t,e){if(e&&("object"===tA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,lA()?Reflect.construct(e,r||[],sA(t).constructor):e.apply(t,r))}function lA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(lA=function(){return!!t})()}function sA(t){return(sA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fA(t,e){return(fA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pA(t,e,r){return(e=hA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hA(t){var e=function(t,e){if("object"!=tA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tA(e)?e:e+""}var yA=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return pA(t=uA(this,e,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),pA(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),pA(t,"getStrokeDasharray",function(r,n,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(n,r);for(var a=Math.floor(r/i),c=r%i,u=n-r,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(iA(o.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(iA(e.repeat(o,a)),iA(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),pA(t,"id",Mn("recharts-line-")),pA(t,"pathRef",function(e){t.mainCurve=e}),pA(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),pA(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fA(t,e)}(e,t.PureComponent),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(iA(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(iA(n),iA(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(ee(t))r=t(e);else{var o=e.key,i=eA(e,Qk),a=jt("recharts-line-dot","boolean"!=typeof t?t.className:"");r=n.createElement(NO,rA({key:o},i,{className:a}))}return r}}],(o=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.points,i=r.xAxis,a=r.yAxis,c=r.layout,u=Jn(r.children,pg);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:Sg(t.payload,e)}},s={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(ho,s,u.map(function(t){return n.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,r,o){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,c=i.points,u=i.dataKey,l=ro(this.props,!1),s=ro(a,!0),f=c.map(function(t,r){var n=oA(oA(oA({key:"dot-".concat(r),r:3},l),s),{},{index:r,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return e.renderDotItem(a,n)}),p={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(o,")"):null};return n.createElement(ho,rA({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,o){var i=this.props,a=i.type,c=i.layout,u=i.connectNulls;i.ref;var l=eA(i,Jk),s=oA(oA(oA({},ro(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},o),{},{type:a,layout:c,connectNulls:u});return n.createElement(Vw,rA({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,o=this.props,i=o.points,a=o.strokeDasharray,c=o.isAnimationActive,u=o.animationBegin,l=o.animationDuration,s=o.animationEasing,f=o.animationId,p=o.animateNewValues,h=o.width,y=o.height,d=this.state,v=d.prevPoints,m=d.totalLength;return n.createElement(OO,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var o=n.t;if(v){var c=v.length/i.length,u=i.map(function(t,e){var r=Math.floor(e*c);if(v[r]){var n=v[r],i=Cn(n.x,t.x),a=Cn(n.y,t.y);return oA(oA({},t),{},{x:i(o),y:a(o)})}if(p){var u=Cn(2*h,t.x),l=Cn(y/2,t.y);return oA(oA({},t),{},{x:u(o),y:l(o)})}return oA(oA({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(u,t,e)}var l,s=Cn(0,m)(o);if(a){var f="".concat(a).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});l=r.getStrokeDasharray(s,m,f)}else l=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(i,t,e,{strokeDasharray:l})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!Jm(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,o=e.dot,i=e.points,a=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,y=e.id;if(r||!i||!i.length)return null;var d=this.state.isAnimationFinished,v=1===i.length,m=jt("recharts-line",a),b=c&&c.allowDataOverflow,g=u&&u.allowDataOverflow,w=b||g,x=Xr(y)?this.id:y,O=null!==(t=ro(o,!1))&&void 0!==t?t:{r:3,strokeWidth:2},j=O.r,S=void 0===j?3:j,P=O.strokeWidth,k=void 0===P?2:P,A=(function(t){return t&&"object"===Vn(t)&&"clipDot"in t}(o)?o:{}).clipDot,E=void 0===A||A,M=2*S+k;return n.createElement(ho,{className:m},b||g?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(x)},n.createElement("rect",{x:b?s:s-f/2,y:g?l:l-p/2,width:b?f:2*f,height:g?p:2*p})),!E&&n.createElement("clipPath",{id:"clipPath-dots-".concat(x)},n.createElement("rect",{x:s-M/2,y:l-M/2,width:f+M,height:p+M}))):null,!v&&this.renderCurve(w,x),this.renderErrorBar(w,x),(v||o)&&this.renderDots(w,E,x),(!h||d)&&kw.renderCallByParent(this.props,i))}}])&&cA(r.prototype,o),i&&cA(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();function dA(t){return(dA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function vA(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,OA(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function mA(t,e,r){return e=gA(e),function(t,e){if(e&&("object"===dA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,bA()?Reflect.construct(e,r||[],gA(t).constructor):e.apply(t,r))}function bA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(bA=function(){return!!t})()}function gA(t){return(gA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function wA(t,e){return(wA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function xA(t,e,r){return(e=OA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function OA(t){var e=function(t,e){if("object"!=dA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dA(e)?e:e+""}function jA(){return jA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},jA.apply(this,arguments)}function SA(t){var e=t.xAxisId,r=jP(),o=SP(),i=xP(e);return null==i?null:n.createElement(Dk,jA({},i,{className:jt("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:o},ticksGenerator:function(t){return Tg(t,!0)}}))}pA(yA,"displayName","Line"),pA(yA,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!Mf.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),pA(yA,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return oA({points:u.map(function(t,e){var u=Sg(t,a);return"horizontal"===s?{x:Lg({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:Xr(u)?null:n.scale(u),value:u,payload:t}:{x:Xr(u)?null:r.scale(u),y:Lg({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var PA=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),mA(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wA(t,e)}(t,n.Component),vA(t,[{key:"render",value:function(){return n.createElement(SA,this.props)}}])}();function kA(t){return(kA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function AA(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,DA(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function EA(t,e,r){return e=_A(e),function(t,e){if(e&&("object"===kA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,MA()?Reflect.construct(e,r||[],_A(t).constructor):e.apply(t,r))}function MA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(MA=function(){return!!t})()}function _A(t){return(_A=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function TA(t,e){return(TA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function CA(t,e,r){return(e=DA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function DA(t){var e=function(t,e){if("object"!=kA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=kA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==kA(e)?e:e+""}function IA(){return IA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},IA.apply(this,arguments)}xA(PA,"displayName","XAxis"),xA(PA,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var NA=function(t){var e=t.yAxisId,r=jP(),o=SP(),i=OP(e);return null==i?null:n.createElement(Dk,IA({},i,{className:jt("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:o},ticksGenerator:function(t){return Tg(t,!0)}}))},BA=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),EA(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&TA(t,e)}(t,n.Component),AA(t,[{key:"render",value:function(){return n.createElement(NA,this.props)}}])}();function LA(t){return function(t){if(Array.isArray(t))return RA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return RA(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return RA(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function RA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}CA(BA,"displayName","YAxis"),CA(BA,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var zA=function(t,e,r,n,o){var i=Jn(t,zP),a=Jn(t,ZP),c=[].concat(LA(i),LA(a)),u=Jn(t,lk),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&lS(e.props,"extendDomain")&&kn(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&lS(e.props,"extendDomain")&&kn(e.props[p])&&kn(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return kn(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},UA={exports:{}};!function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=new Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=new Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c}(UA);var $A=new(r(UA.exports)),qA="recharts.syncMouseEvents";function FA(t){return(FA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function WA(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,VA(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function HA(t,e,r){return(e=VA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function VA(t){var e=function(t,e){if("object"!=FA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=FA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==FA(e)?e:e+""}var XA=function(){return WA(function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),HA(this,"activeIndex",0),HA(this,"coordinateList",[]),HA(this,"layout","horizontal")},[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+c,s=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])}();function GA(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[Jg(e,r,n,o),Jg(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function YA(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return GA(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=Jg(c,u,l,f),h=Jg(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}function KA(t){return(KA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ZA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function JA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ZA(Object(r),!0).forEach(function(e){QA(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ZA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function QA(t,e,r){var n;return n=function(t,e){if("object"!=KA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=KA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==KA(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tE(e){var r,n,o,i=e.element,a=e.tooltipEventType,c=e.isActive,u=e.activeCoordinate,l=e.activePayload,s=e.offset,f=e.activeTooltipIndex,p=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,d=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!d||!c||!u||"ScatterChart"!==y&&"axis"!==a)return null;var v=Vw;if("ScatterChart"===y)o=u,v=FO;else if("BarChart"===y)o=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(h,u,s,p),v=DO;else if("radial"===h){var m=GA(u),b=m.cx,g=m.cy,w=m.radius;o={cx:b,cy:g,startAngle:m.startAngle,endAngle:m.endAngle,innerRadius:w,outerRadius:w},v=Nw}else o={points:YA(h,u,s)},v=Vw;var x=JA(JA(JA(JA({stroke:"#ccc",pointerEvents:"none"},s),o),ro(d,!1)),{},{payload:l,payloadIndex:f,className:jt("recharts-tooltip-cursor",d.className)});return t.isValidElement(d)?t.cloneElement(d,x):t.createElement(v,x)}var eE=["item"],rE=["children","className","width","height","style","compact","title","desc"];function nE(t){return(nE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function oE(){return oE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},oE.apply(this,arguments)}function iE(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hE(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aE(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function cE(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,bE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function uE(t,e,r){return e=sE(e),function(t,e){if(e&&("object"===nE(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,lE()?Reflect.construct(e,r||[],sE(t).constructor):e.apply(t,r))}function lE(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(lE=function(){return!!t})()}function sE(t){return(sE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fE(t,e){return(fE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pE(t){return function(t){if(Array.isArray(t))return yE(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hE(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hE(t,e){if(t){if("string"==typeof t)return yE(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?yE(t,e):void 0}}function yE(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function dE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function vE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dE(Object(r),!0).forEach(function(e){mE(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function mE(t,e,r){return(e=bE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function bE(t){var e=function(t,e){if("object"!=nE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nE(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nE(e)?e:e+""}var gE={xAxis:["bottom","top"],yAxis:["left","right"]},wE={width:"100%",height:"100%"},xE={x:0,y:0};function OE(t){return t}var jE=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(pE(t),pE(r)):t},[]);return i.length>0?i:t&&t.length&&kn(n)&&kn(o)?t.slice(n,o+1):[]};function SE(t){return"number"===t?[0,"auto"]:void 0}var PE=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=jE(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory)?l=Dn(void 0===s?a:s,i.dataKey,n):l=s&&s[r]||a[r];return l?[].concat(pE(o),[Hg(c,l)]):o},[])},kE=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(Sn(s-l)!==Sn(f-s)){var h=[];if(Sn(f-s)===Sn(c[1]-c[0])){p=f;var y=s+c[1]-c[0];h[0]=Math.min(y,(y+l)/2),h[1]=Math.max(y,(y+l)/2)}else{p=l;var d=f+c[1]-c[0];h[0]=Math.min(s,(d+s)/2),h[1]=Math.max(s,(d+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i}(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=PE(t,e,l,s),p=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return vE(vE(vE({},n),Jg(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return vE(vE(vE({},n),Jg(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return xE}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},AE=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=Mg(l,o);return r.reduce(function(e,r){var h,y=void 0!==r.type.defaultProps?vE(vE({},r.type.defaultProps),r.props):r.props,d=y.type,v=y.dataKey,m=y.allowDataOverflow,b=y.allowDuplicatedCategory,g=y.scale,w=y.ticks,x=y.includeHidden,O=y[i];if(e[O])return e;var j,S,P,k=jE(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),A=k.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&kn(n)&&kn(o))return!0}return!1})(y.domain,m,d)&&(j=qg(y.domain,null,m),!p||"number"!==d&&"auto"===g||(P=Pg(k,v,"category")));var E=SE(d);if(!j||0===j.length){var M,_=null!==(M=y.domain)&&void 0!==M?M:E;if(v){if(j=Pg(k,v,d),"category"===d&&p){var T=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1}(j);b&&T?(S=j,j=Lj(0,A)):b||(j=Wg(_,j,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(pE(t),[e])},[]))}else if("category"===d)j=b?j.filter(function(t){return""!==t&&!Xr(t)}):Wg(_,j,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||Xr(e)?t:[].concat(pE(t),[e])},[]);else if("number"===d){var C=function(t,e,r,n,o){var i=e.map(function(e){return Ag(t,e,r,o,n)}).filter(function(t){return!Xr(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null}(k,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)}),v,o,l);C&&(j=C)}!p||"number"!==d&&"auto"===g||(P=Pg(k,v,"category"))}else j=p?Lj(0,A):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:zg(a[O].stackGroups,c,u):Eg(k,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);if("number"===d)j=zA(s,j,O,o,w),_&&(j=qg(_,j,m));else if("category"===d&&_){var D=_;j.every(function(t){return D.indexOf(t)>=0})&&(j=D)}}return vE(vE({},e),{},mE({},O,vE(vE({},y),{},{axisType:o,domain:j,categoricalDomain:P,duplicateDomain:S,originalDomain:null!==(h=y.domain)&&void 0!==h?h:E,isCategorical:p,layout:l})))},{})},EE=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=Jn(l,o),p={};return f&&f.length?p=AE(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=jE(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=Mg(l,o),y=-1;return r.reduce(function(t,e){var d,v=(void 0!==e.type.defaultProps?vE(vE({},e.type.defaultProps),e.props):e.props)[i],m=SE("number");return t[v]?t:(y++,h?d=Lj(0,p):a&&a[v]&&a[v].hasStack?(d=zg(a[v].stackGroups,c,u),d=zA(s,d,v,o)):(d=qg(m,Eg(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),d=zA(s,d,v,o)),vE(vE({},t),{},mE({},v,vE(vE({axisType:o},n.defaultProps),{},{hide:!0,orientation:Vr(gE,"".concat(o,".").concat(y%2),null),domain:d,originalDomain:m,isCategorical:h,layout:l}))))},{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},ME=function(t){var e=t.children,r=t.defaultShowTooltip,n=Qn(e,eS),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},_E=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},TE=function(t,e){var r=t.props,n=(t.graphicalItems,t.xAxisMap),o=void 0===n?{}:n,i=t.yAxisMap,a=void 0===i?{}:i,c=r.width,u=r.height,l=r.children,s=r.margin||{},f=Qn(l,eS),p=Qn(l,as),h=Object.keys(a).reduce(function(t,e){var r=a[e],n=r.orientation;return r.mirror||r.hide?t:vE(vE({},t),{},mE({},n,t[n]+r.width))},{left:s.left||0,right:s.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:vE(vE({},t),{},mE({},n,Vr(t,"".concat(n))+r.height))},{top:s.top||0,bottom:s.bottom||0}),d=vE(vE({},y),h),v=d.bottom;f&&(d.bottom+=f.props.height||eS.defaultProps.height),p&&e&&(d=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=i-(a.left||0)-(a.right||0),u=mg({children:o,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,y=u.layout;if(("vertical"===y||"horizontal"===y&&"middle"===h)&&"center"!==p&&kn(t[p]))return Og(Og({},t),{},jg({},p,t[p]+(s||0)));if(("horizontal"===y||"vertical"===y&&"center"===p)&&"middle"!==h&&kn(t[h]))return Og(Og({},t),{},jg({},h,t[h]+(f||0)))}return t}(d,0,r,e));var m=c-d.left-d.right,b=u-d.top-d.bottom;return vE(vE({brushBottom:v},d),{},{width:Math.max(m,0),height:Math.max(b,0)})},CE=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},DE=function(e){var r=e.chartName,o=e.GraphicalChild,i=e.defaultTooltipEventType,a=void 0===i?"axis":i,c=e.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=e.axisComponents,s=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,y=_E(s),d=y.numericAxisName,v=y.cateAxisName,m=function(t){return!(!t||!t.length)&&t.some(function(t){var e=Gn(t&&t.type);return e&&e.indexOf("Bar")>=0})}(r),b=[];return r.forEach(function(r,y){var g=jE(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),w=void 0!==r.type.defaultProps?vE(vE({},r.type.defaultProps),r.props):r.props,x=w.dataKey,O=w.maxBarSize,j=w["".concat(d,"Id")],S=w["".concat(v,"Id")],P=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||Jb();var i=n[o];return vE(vE({},t),{},mE(mE({},r.axisType,i),"".concat(r.axisType,"Ticks"),Tg(i)))},{}),k=P[v],A=P["".concat(v,"Ticks")],E=n&&n[j]&&n[j].hasStack&&function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?Og(Og({},t.type.defaultProps),t.props):t.props).stackId;if(An(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null}(r,n[j].stackGroups),M=Gn(r.type).indexOf("Bar")>=0,_=Fg(k,A),T=[],C=m&&function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],y=h.items,d=h.cateAxisId,v=y.filter(function(t){return Gn(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?Og(Og({},m),v[0].props):v[0].props,g=b.barSize,w=b[d];i[w]||(i[w]=[]);var x=Xr(g)?e:g;i[w].push({item:v[0],stackList:v.slice(1),barSize:Xr(x)?void 0:_n(x,r,0)})}}return i}({barSize:u,stackGroups:n,totalSize:CE(P,v)});if(M){var D,I,N=Xr(O)?h:O,B=null!==(D=null!==(I=Fg(k,A,!0))&&void 0!==I?I:N)&&void 0!==D?D:0;T=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,c=i.length;if(c<1)return null;var u,l=_n(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/c,h=i.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=n&&(h-=(c-1)*l,l=0),h>=n&&p>0&&(f=!0,h=c*(p*=.9));var y={offset:((n-h)/2|0)-l,size:0};u=i.reduce(function(t,e){var r={item:e.item,position:{offset:y.offset+y.size+l,size:f?p:e.barSize}},n=[].concat(gg(t),[r]);return y=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:y})}),n},s)}else{var d=_n(r,n,0,!0);n-2*d-(c-1)*l<=0&&(l=0);var v=(n-2*d-(c-1)*l)/c;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;u=i.reduce(function(t,e,r){var n=[].concat(gg(t),[{item:e.item,position:{offset:d+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return u}({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:C[S],maxBarSize:N}),B!==_&&(T=T.map(function(t){return vE(vE({},t),{},{position:vE(vE({},t.position),{},{offset:t.position.offset-B/2})})}))}var L,R,z=r&&r.type&&r.type.getComposedData;z&&b.push({props:vE(vE({},z(vE(vE({},P),{},{displayedData:g,props:t,dataKey:x,item:r,bandSize:_,barPosition:T,offset:o,stackedData:E,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},mE(mE(mE({key:r.key||"item-".concat(y)},d,P[d]),v,P[v]),"animationId",i)),childIndex:(L=r,R=t.children,Zn(R).indexOf(L)),item:r})}),b},y=function(t,e){var n=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!to({props:n}))return null;var u=n.children,s=n.layout,p=n.stackOffset,y=n.data,d=n.reverseStackOrder,v=_E(s),m=v.numericAxisName,b=v.cateAxisName,g=Jn(u,o),w=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?Og(Og({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(An(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[Mn("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return Og(Og({},t),{},jg({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return Og(Og({},e),{},jg({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:Bg(t,a.items,o)}))},{})),Og(Og({},e),{},jg({},i,c))},{})}(y,g,"".concat(m,"Id"),"".concat(b,"Id"),p,d),x=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return vE(vE({},t),{},mE({},r,EE(n,vE(vE({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&w,dataStartIndex:i,dataEndIndex:a}))))},{}),O=TE(vE(vE({},x),{},{props:n,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(x).forEach(function(t){x[t]=f(n,x[t],O,t.replace("Map",""),r)});var j,S,P=x["".concat(b,"Map")],k=(j=Tn(P),{tooltipTicks:S=Tg(j,!1,!0),orderedTooltipTicks:ef(S,function(t){return t.coordinate}),tooltipAxis:j,tooltipAxisBandSize:Fg(j,S)}),A=h(n,vE(vE({},x),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:w,offset:O}));return vE(vE({formattedGraphicalItems:A,graphicalItems:g,offset:O,stackGroups:w},k),x)},d=function(){function e(o){var i,a,c;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),mE(c=uE(this,e,[o]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),mE(c,"accessibilityManager",new XA),mE(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(vE({legendBBox:t},y({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},vE(vE({},c.state),{},{legendBBox:t}))))}}),mE(c,"handleReceiveSyncEvent",function(t,e,r){if(c.props.syncId===t){if(r===c.eventEmitterSymbol&&"function"!=typeof c.props.syncMethod)return;c.applySyncEvent(e)}}),mE(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return vE({dataStartIndex:e,dataEndIndex:r},y({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),mE(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=vE(vE({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;ee(n)&&n(r,t)}}),mE(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?vE(vE({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;ee(n)&&n(r,t)}),mE(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),mE(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),mE(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),mE(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;ee(r)&&r(e,t)}),mE(c,"handleOuterEvent",function(t){var e,r=function(t){var e=t&&t.type;return e&&Xn[e]?Xn[e]:null}(t),n=Vr(c.props,"".concat(r));r&&ee(n)&&n(null!==(e=/.*touch.*/i.test(r)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)}),mE(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=vE(vE({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;ee(n)&&n(r,t)}}),mE(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;ee(e)&&e(c.getMouseInfo(t),t)}),mE(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;ee(e)&&e(c.getMouseInfo(t),t)}),mE(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),mE(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),mE(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),mE(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;ee(e)&&e(c.getMouseInfo(t),t)}),mE(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;ee(e)&&e(c.getMouseInfo(t),t)}),mE(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&$A.emit(qA,c.props.syncId,t,c.eventEmitterSymbol)}),mE(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(vE({dataStartIndex:i,dataEndIndex:a},y({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var v=vE(vE({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,w=PE(c.state,c.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:xE;c.setState(vE(vE({},t),{},{activeLabel:g,activeCoordinate:x,activePayload:w,activeTooltipIndex:s}))}else c.setState(t)}),mE(c,"renderCursor",function(t){var e,o=c.state,i=o.isTooltipActive,a=o.activeCoordinate,u=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!==(e=t.props.active)&&void 0!==e?e:i,y=c.props.layout,d=t.key||"_recharts-cursor";return n.createElement(tE,{key:d,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:r,element:t,isActive:h,layout:y,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),mE(c,"renderPolarAxis",function(e,r,n){var o=Vr(e,"type.axisType"),i=Vr(c.state,"".concat(o,"Map")),a=e.type.defaultProps,u=void 0!==a?vE(vE({},a),e.props):e.props,l=i&&i[u["".concat(o,"Id")]];return t.cloneElement(e,vE(vE({},l),{},{className:jt(o,l.className),key:e.key||"".concat(r,"-").concat(n),ticks:Tg(l,!0)}))}),mE(c,"renderPolarGrid",function(e){var r=e.props,n=r.radialLines,o=r.polarAngles,i=r.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=Tn(u),f=Tn(l),p=f.cx,h=f.cy,y=f.innerRadius,d=f.outerRadius;return t.cloneElement(e,{polarAngles:Array.isArray(o)?o:Tg(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:Tg(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:y,outerRadius:d,key:e.key||"polar-grid",radialLines:n})}),mE(c,"renderLegend",function(){var e=c.state.formattedGraphicalItems,r=c.props,n=r.children,o=r.width,i=r.height,a=c.props.margin||{},u=o-(a.left||0)-(a.right||0),l=mg({children:n,formattedGraphicalItems:e,legendWidth:u,legendContent:s});if(!l)return null;var f=l.item,p=aE(l,eE);return t.cloneElement(f,vE(vE({},p),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),mE(c,"renderTooltip",function(){var e,r=c.props,n=r.children,o=r.accessibilityLayer,i=Qn(n,$f);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(e=i.props.active)&&void 0!==e?e:u;return t.cloneElement(i,{viewBox:vE(vE({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})}),mE(c,"renderBrush",function(e){var r=c.props,n=r.margin,o=r.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return t.cloneElement(e,{key:e.key||"_recharts-brush",onChange:Dg(c.handleBrushChange,e.props.onChange),data:o,x:kn(e.props.x)?e.props.x:a.left,y:kn(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(n.bottom||0),width:kn(e.props.width)?e.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),mE(c,"renderReferenceElement",function(e,r,n){if(!e)return null;var o=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=e.type.defaultProps||{},f=e.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,y=f.yAxisId,d=void 0===y?s.yAxisId:y;return t.cloneElement(e,{key:e.key||"".concat(r,"-").concat(n),xAxis:a[h],yAxis:u[d],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),mE(c,"renderActivePoints",function(t){var r=t.item,n=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=r.props.key,l=void 0!==r.item.type.defaultProps?vE(vE({},r.item.type.defaultProps),r.item.props):r.item.props,s=l.activeDot,f=vE(vE({index:i,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:kg(r.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},ro(s,!1)),$n(s));return c.push(e.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(e.renderActiveDot(s,vE(vE({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),mE(c,"renderGraphicChild",function(e,r,n){var o=c.filterFormatItem(e,r,n);if(!o)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=Qn(c.props.children,$f),h=o.props,y=h.points,d=h.isRange,v=h.baseLine,m=void 0!==o.item.type.defaultProps?vE(vE({},o.item.type.defaultProps),o.item.props):o.item.props,b=m.activeDot,g=m.hide,w=m.activeBar,x=m.activeShape,O=Boolean(!g&&u&&p&&(b||w||x)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:Dg(c.handleItemMouseEnter,e.props.onClick)}:"axis"!==i&&(j={onMouseLeave:Dg(c.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:Dg(c.handleItemMouseEnter,e.props.onMouseEnter)});var S=t.cloneElement(e,vE(vE({},o.props),j));if(O){if(!(s>=0)){var P,k=(null!==(P=c.getItemByXY(c.state.activeCoordinate))&&void 0!==P?P:{graphicalItem:S}).graphicalItem,A=k.item,E=void 0===A?e:A,M=k.childIndex,_=vE(vE(vE({},o.props),j),{},{activeIndex:M});return[t.cloneElement(E,_),null,null]}var T,C;if(l.dataKey&&!l.allowDuplicatedCategory){var D="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());T=Dn(y,D,f),C=d&&v&&Dn(v,D,f)}else T=null==y?void 0:y[s],C=d&&v&&v[s];if(x||w){var I=void 0!==e.props.activeIndex?e.props.activeIndex:s;return[t.cloneElement(e,vE(vE(vE({},o.props),j),{},{activeIndex:I})),null,null]}if(!Xr(T))return[S].concat(pE(c.renderActivePoints({item:o,activePoint:T,basePoint:C,childIndex:s,isRange:d})))}return d?[S,null,null]:[S,null]}),mE(c,"renderCustomized",function(e,r,n){return t.cloneElement(e,vE(vE({key:"recharts-customized-".concat(n)},c.props),c.state))}),mE(c,"renderMap",{CartesianGrid:{handler:OE,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:OE},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:OE},YAxis:{handler:OE},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(i=o.id)&&void 0!==i?i:Mn("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=cp(c.triggeredAfterMouseMove,null!==(a=o.throttleDelay)&&void 0!==a?a:1e3/60),c.state={},c}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fE(t,e)}(e,t.Component),cE(e,[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=Qn(e,$f);if(i){var a=i.props.defaultIndex;if(!("number"!=typeof a||a<0||a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=PE(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=vE(vE({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;var r,n;(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin)&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}});return null}},{key:"componentDidUpdate",value:function(t){no([Qn(t.children,$f)],[Qn(this.props.children,$f)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=Qn(this.props.children,$f);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e,r=this.container,n=r.getBoundingClientRect(),o={top:(e=n).top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-o.left),chartY:Math.round(t.pageY-o.top)},a=n.width/r.offsetWidth||1,c=this.inRange(i.chartX,i.chartY,a);if(!c)return null;var u=this.state,l=u.xAxisMap,s=u.yAxisMap,f=this.getTooltipEventType(),p=kE(this.state,this.props.data,this.props.layout,c);if("axis"!==f&&l&&s){var h=Tn(l).scale,y=Tn(s).scale,d=h&&h.invert?h.invert(i.chartX):null,v=y&&y.invert?y.invert(i.chartY):null;return vE(vE({},i),{},{xValue:d,yValue:v},p)}return p?vE(vE({},i),p):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=Tn(u);return ew({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=Qn(t,$f),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),vE(vE({},$n(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){$A.on(qA,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){$A.removeListener(qA,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===Gn(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=iE(e,2),n=r[0],o=r[1];return vE(vE({},t),{},mE({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=iE(e,2),n=r[0],o=r[1];return vE(vE({},t),{},mE({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?vE(vE({},u.type.defaultProps),u.props):u.props,s=Gn(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return TO(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return ew(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(Oj(a,n)||jj(a,n)||Sj(a,n)){var h=Ej({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),y=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:vE(vE({},a),{},{childIndex:y}),payload:Sj(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t=this;if(!to(this))return null;var e,r,o=this.props,i=o.children,a=o.className,c=o.width,u=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,h=aE(o,rE),y=ro(h,!1);if(s)return n.createElement(wP,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(lo,oE({},y,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),io(i,this.renderMap)));this.props.accessibilityLayer&&(y.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,y.role=null!==(r=this.props.role)&&void 0!==r?r:"application",y.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},y.onFocus=function(){t.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return n.createElement(wP,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",oE({className:jt("recharts-wrapper",a),style:vE({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(e){t.container=e}}),n.createElement(lo,oE({},y,{width:c,height:u,title:f,desc:p,style:wE}),this.renderClipPath(),io(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}();mE(d,"displayName",r),mE(d,"defaultProps",vE({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),mE(d,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=ME(t);return vE(vE(vE({},p),{},{updateId:0},y(vE(vE({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!Nn(l,e.prevMargin)){var h=ME(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=vE(vE({},kE(e,n,c)),{},{updateId:e.updateId+1}),m=vE(vE(vE({},h),d),v);return vE(vE(vE({},m),y(vE({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!no(o,e.prevChildren)){var b,g,w,x,O=Qn(o,eS),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,S=O&&null!==(w=null===(x=O.props)||void 0===x?void 0:x.endIndex)&&void 0!==w?w:f,P=j!==s||S!==f,k=!Xr(n)&&!P?e.updateId:e.updateId+1;return vE(vE({updateId:k},y(vE(vE({props:t},e),{},{updateId:k,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),mE(d,"renderActiveDot",function(e,r,o){var i;return i=t.isValidElement(e)?t.cloneElement(e,r):ee(e)?e(r):n.createElement(NO,r),n.createElement(ho,{className:"recharts-active-dot",key:o},i)});var v=t.forwardRef(function(t,e){return n.createElement(d,oE({},t,{ref:e}))});return v.displayName=d.displayName,v},IE=DE({chartName:"LineChart",GraphicalChild:yA,axisComponents:[{axisType:"xAxis",AxisComp:PA},{axisType:"yAxis",AxisComp:BA}],formatAxisMap:JS}),NE=DE({chartName:"BarChart",GraphicalChild:HS,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:PA},{axisType:"yAxis",AxisComp:BA}],formatAxisMap:JS});export{S as $,a as A,p as B,d as C,N as D,A as E,M as F,D as G,B as H,L as I,T as J,gt as K,q as L,V as M,P as N,J as O,K as P,tt as Q,yp as R,et as S,ht as T,vt as U,ct as V,W,wt as X,BA as Y,NE as Z,HS as _,c as a,dt as a0,ft as a1,yt as a2,Y as a3,v as a4,I as a5,bt as a6,Q as a7,$ as a8,it as a9,O as aa,x as ab,C as ac,w as ad,s as ae,m as af,lt as ag,k as ah,R as ai,xt as aj,ot as ak,Z as al,G as am,j as an,H as ao,nt as b,y as c,h as d,at as e,f,mt as g,b as h,l as i,g as j,ut as k,rt as l,X as m,F as n,pt as o,u as p,U as q,E as r,z as s,st as t,IE as u,Zk as v,PA as w,$f as x,yA as y,_ as z};
