"use client"

import { useState, useEffect } from "react"
import { Search, Filter, Edit, Trash2, Eye, Upload, FileText, Video, HeadphonesIcon } from "lucide-react"
import { useAuth } from "../../contexts/AuthContext"
import { UploadMaterialModal } from "../../components/admin/UploadMaterialModal"
import { API_ENDPOINTS, apiUtils } from "../../config/api"

function AdminMaterials() {
  const { user } = useAuth()
  const [searchTerm, setSearchTerm] = useState("")
  const [filter, setFilter] = useState("all")
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [materials, setMaterials] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch materials from API with admin authentication
  const fetchMaterials = async () => {
    try {
      setLoading(true)
      const response = await apiUtils.get(API_ENDPOINTS.STUDY_MATERIALS)
      if (response.ok) {
        const data = await response.json()
        setMaterials(data)
      }
    } catch (error) {
      console.error("Error fetching materials:", error)
    } finally {
      setLoading(false)
    }
  }

  // Load materials on component mount
  useEffect(() => {
    fetchMaterials()
  }, [])

  const filteredMaterials = materials.filter((material) => {
    const matchesSearch =
      material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter =
      filter === "all" || material.status === filter || material.type === filter || material.subject === filter
    return matchesSearch && matchesFilter
  })

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Video":
        return <Video className="h-5 w-5 text-red-600" />
      case "Audio":
        return <HeadphonesIcon className="h-5 w-5 text-purple-600" />
      default:
        return <FileText className="h-5 w-5 text-blue-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Published</span>
      case "draft":
        return <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">Draft</span>
      case "archived":
        return <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">Archived</span>
      default:
        return null
    }
  }

  const handleUpload = async (file: File, materialData: any) => {
    try {
      setUploading(true)

      // Get Firebase auth token
      const { getAuth } = await import("firebase/auth")
      const auth = getAuth()
      const currentUser = auth.currentUser

      if (!currentUser) {
        throw new Error("User not authenticated")
      }

      const token = await currentUser.getIdToken()

      // Create FormData for multipart upload
      const formData = new FormData()
      formData.append("file", file)
      formData.append("title", materialData.title)
      formData.append("description", materialData.description || "")
      formData.append("subject", materialData.subject)
      formData.append("price", materialData.price)
      formData.append("author", materialData.author)
      formData.append("tags", materialData.tags || "")
      formData.append("isPremium", materialData.isPremium)

      if (materialData.originalPrice) {
        formData.append("originalPrice", materialData.originalPrice)
      }
      if (materialData.pages) {
        formData.append("pages", materialData.pages)
      }
      if (materialData.thumbnailUrl) {
        formData.append("thumbnailUrl", materialData.thumbnailUrl)
      }
      formData.append("status", materialData.status || "published")

      // Upload to our Spring Boot backend
      const response = await fetch(`${API_ENDPOINTS.STUDY_MATERIALS}/upload`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          // Don't set Content-Type for FormData, let browser set it with boundary
        },
        body: formData,
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Upload failed: ${response.status} - ${errorText}`)
      }

      const savedMaterial = await response.json()

      // Update local state
      setMaterials((prev) => [...prev, savedMaterial])
      setShowUploadModal(false)
      setUploading(false)

      alert("Material uploaded successfully!")
    } catch (error: any) {
      console.error("Upload failed:", error)
      setUploading(false)
      alert(`Upload failed: ${error.message}`)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Study Materials</h1>
          <p className="text-gray-600 mt-1">Manage study materials and content</p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 mt-4 sm:mt-0"
        >
          <Upload className="h-4 w-4" />
          <span>Upload Material</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex items-center space-x-4">
            <Filter className="h-5 w-5 text-gray-600" />
            <div className="flex space-x-2">
              {["all", "published", "draft", "PDF", "Video", "Audio", "Current Affairs", "Polity", "Economics"].map(
                (filterOption) => (
                  <button
                    key={filterOption}
                    onClick={() => setFilter(filterOption)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      filter === filterOption ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    {filterOption}
                  </button>
                ),
              )}
            </div>
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
            />
          </div>
        </div>
      </div>

      {/* Materials Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Material</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Type</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Subject</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Price</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Performance</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-500">
                    Loading materials...
                  </td>
                </tr>
              ) : filteredMaterials.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-500">
                    No materials found
                  </td>
                </tr>
              ) : (
                filteredMaterials.map((material) => (
                  <tr key={material.id} className="hover:bg-gray-50">
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3">
                        {getTypeIcon(material.type)}
                        <div>
                          <p className="font-medium text-gray-900">{material.title}</p>
                          <p className="text-sm text-gray-600">{material.description}</p>
                          <p className="text-xs text-gray-500">By {material.author}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="text-sm">
                        <p className="text-gray-900">{material.type}</p>
                        <p className="text-gray-600">{material.fileSize}</p>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                        {material.subject}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="text-lg font-bold text-green-600">₹{material.price}</span>
                    </td>
                    <td className="py-4 px-6">{getStatusBadge(material.status)}</td>
                    <td className="py-4 px-6">
                      <div className="text-sm">
                        <p className="text-gray-900">{material.downloads} downloads</p>
                        {material.rating > 0 && <p className="text-gray-600">★ {material.rating}</p>}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex space-x-2">
                        <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Upload Material Modal */}
      {showUploadModal && (
        <UploadMaterialModal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          onUpload={handleUpload}
          uploading={uploading}
        />
      )}
    </div>
  )
}

export default AdminMaterials
